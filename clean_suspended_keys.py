#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理被暂停的 Gemini API 密钥脚本
自动从 .env 文件中移除所有被 Google 暂停的 API 密钥
"""

import os
import re
import json
from pathlib import Path

# 被暂停的 API 密钥列表（从日志分析得出）
SUSPENDED_KEYS = [
    "AIzaSyBxkvVTOJb8zvOdYxdEeJMadDPRv8sXwJ0",
    "AIzaSyCMyEGcCIwXahRjzIunh-KO_psO0U9ZmnE", 
    "AIzaSyDmJKwf_O9gSY0C9kaCkEQpct3puSU3pL4",
    "AIzaSyASzSAipilXUUftuTgP7SuzWcr8goXaD30",
    "AIzaSyDFtn4L4QnG6f_T_oy-QU3RCnSjGspeKeE",
    "AIzaSyAT6rPzhAMiwOnjWWLscAfxLvdRaGFJeNg",
    "AIzaSyDqw2P__xctNxWt2JaR3IGCbk_NLU_OJDU",
    "AIzaSyCZch0ezSzEcnUeB60HZWb-uzvUt4SfW0I",
    "AIzaSyDaroOULn3FdeJASLqlosWr0RXDSZt2-x0",
    "AIzaSyCMjX6TlBujpII8WX9NOtnYW9COP9BvUzg"
]

def clean_api_keys():
    """清理 .env 文件中被暂停的 API 密钥"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env 文件不存在")
        return False
    
    # 读取 .env 文件
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔍 分析 .env 文件中的 API_KEYS...")
    
    # 查找 API_KEYS 行
    api_keys_pattern = r'API_KEYS\s*=\s*(\[.*?\])'
    match = re.search(api_keys_pattern, content, re.DOTALL)
    
    if not match:
        print("❌ 未找到 API_KEYS 配置")
        return False
    
    try:
        # 解析现有的 API 密钥
        keys_str = match.group(1)
        current_keys = json.loads(keys_str)
        
        print(f"📊 当前配置的密钥数量: {len(current_keys)}")
        
        # 过滤掉被暂停的密钥
        valid_keys = []
        removed_keys = []
        
        for key in current_keys:
            if key in SUSPENDED_KEYS:
                removed_keys.append(key)
                print(f"🚫 移除被暂停的密钥: {key[:20]}...")
            else:
                valid_keys.append(key)
        
        if not removed_keys:
            print("✅ 没有发现被暂停的密钥")
            return True
        
        print(f"📈 移除了 {len(removed_keys)} 个被暂停的密钥")
        print(f"📈 剩余有效密钥: {len(valid_keys)} 个")
        
        if len(valid_keys) == 0:
            print("⚠️  警告: 所有密钥都被移除了，添加占位符")
            valid_keys = ["your_valid_gemini_api_key_here"]
        
        # 生成新的 API_KEYS 配置
        new_keys_str = json.dumps(valid_keys, indent=0)
        new_content = re.sub(api_keys_pattern, f'API_KEYS={new_keys_str}', content, flags=re.DOTALL)
        
        # 备份原文件
        backup_file = env_file.with_suffix('.env.backup')
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"💾 原文件已备份到: {backup_file}")
        
        # 写入清理后的内容
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ .env 文件已更新")
        print("\n📋 移除的密钥列表:")
        for i, key in enumerate(removed_keys, 1):
            print(f"  {i}. {key}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 解析 API_KEYS 失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🧹 Gemini API 密钥清理工具")
    print("=" * 50)
    
    if clean_api_keys():
        print("\n🎉 清理完成！")
        print("\n📝 后续步骤:")
        print("1. 获取新的有效 Gemini API 密钥")
        print("2. 更新 .env 文件中的 API_KEYS")
        print("3. 重启服务: py gemini_balance_service.py restart")
        print("\n🔗 获取 API 密钥: https://makersuite.google.com/app/apikey")
    else:
        print("\n❌ 清理失败，请检查错误信息")

if __name__ == "__main__":
    main()
