# API 密钥统计分析报告
# 生成时间: 2025-07-31 03:22
# 基于日志文件: logs/app_stdout.log (14722行)

========================================
🔍 API 密钥数量变化历史
========================================

## KeyManager 实例创建记录

**时间线分析**:

1. **03:04:05** - 初始启动
   ```
   KeyManager instance created/re-created with 2 API keys and 1 Vertex Express API keys
   ```
   - 状态: 初始配置，使用 .env 文件的配置
   - API 密钥数量: 2 个

2. **03:04:50** - 配置更新后
   ```
   KeyManager instance created/re-created with 28 API keys and 1 Vertex Express API keys
   ```
   - 状态: 通过管理界面更新配置
   - API 密钥数量: 28 个 (包含被暂停的密钥)

3. **03:19:28** - 第一次清理后
   ```
   KeyManager instance created/re-created with 24 API keys and 1 Vertex Express API keys
   ```
   - 状态: 部分清理被暂停的密钥
   - API 密钥数量: 24 个 (减少了 4 个)

4. **03:27:48** - 第二次清理后
   ```
   KeyManager instance created/re-created with 17 API keys and 1 Vertex Express API keys
   ```
   - 状态: 进一步清理被暂停的密钥
   - API 密钥数量: 17 个 (减少了 7 个)

5. **03:42:19** - 最新状态 (服务重启后)
   ```
   KeyManager instance created/re-created with 17 API keys and 1 Vertex Express API keys
   ```
   - 状态: 当前使用的密钥数量
   - API 密钥数量: 17 个 (保持不变)

========================================
📊 当前 API 密钥状态分析
========================================

## 当前配置
- **总 API 密钥数量**: 17 个
- **Vertex Express API 密钥**: 1 个
- **最后更新时间**: 03:42:19

## 密钥清理进度
- **原始密钥数量**: 28 个
- **已清理密钥数量**: 11 个 (28 - 17)
- **清理进度**: 39.3% (11/28)

## 从日志中识别的有效密钥 (部分显示)
✅ **正常工作的密钥**:
1. AIzaSy...R55kXk - 成功请求 (行 78-79)
2. AIzaSy...71ZnR0 - 成功请求 (行 83-84)
3. AIzaSy...wJaBPE - 重试后成功 (行 194-196)

⚠️ **有问题的密钥**:
1. AIzaSy...tdjjgk - additionalProperties 错误 (行 91)
2. AIzaSy...KwuqH0 - additionalProperties 错误 (行 94)
3. AIzaSy...Arv4Pg - additionalProperties 错误 (行 297)

🚫 **确认被暂停的密钥**:
1. AIzaSyBxkvVTOJb8zvOdYxdEeJMadDPRv8sXwJ0 - CONSUMER_SUSPENDED (行 404)
2. AIzaSyCMyEGcCIwXahRjzIunh-KO_psO0U9ZmnE - CONSUMER_SUSPENDED (行 512)
3. AIzaSyDmJKwf_O9gSY0C9kaCkEQpct3puSU3pL4 - CONSUMER_SUSPENDED (行 620)
4. AIzaSyASzSAipilXUUftuTgP7SuzWcr8goXaD30 - CONSUMER_SUSPENDED (行 731)

========================================
🔧 错误类型统计
========================================

## 从最新日志分析 (行 1-825)

### 1. additionalProperties 错误 (400 INVALID_ARGUMENT)
- **错误次数**: 6 次
- **影响密钥**: 3 个
- **错误模式**: JSON 格式问题
- **状态**: 已修复代码，需要重启服务

### 2. CONSUMER_SUSPENDED 错误 (403 PERMISSION_DENIED)
- **错误次数**: 8 次
- **影响密钥**: 4 个被暂停的密钥
- **错误模式**: 密钥被 Google 永久暂停
- **状态**: 需要从配置中完全移除

### 3. 成功请求 (200 OK)
- **成功次数**: 4 次
- **有效密钥**: 至少 2 个确认有效
- **成功率**: 约 25% (4/16 总请求)

========================================
🎯 密钥质量评估
========================================

## 基于当前 17 个密钥的估算

### 密钥分类 (估算)
- **完全有效密钥**: ~6 个 (35%)
- **部分有效密钥**: ~7 个 (41%) - 有 additionalProperties 问题
- **被暂停密钥**: ~4 个 (24%) - 需要移除

### 预期成功率
- **修复前**: 25-30% (additionalProperties 问题影响)
- **修复后**: 75-80% (移除被暂停密钥后)

========================================
🚀 优化建议
========================================

## 立即行动
1. **重启服务** - 应用 additionalProperties 修复
2. **移除被暂停密钥** - 清理剩余的 4 个被暂停密钥
3. **监控成功率** - 验证修复效果

## 预期效果
- **API 成功率**: 从 25% 提升至 80%+
- **有效密钥数量**: 13 个 (17 - 4 个被暂停)
- **错误率**: 显著降低

## 长期优化
1. **添加新密钥** - 增加到 20+ 个有效密钥
2. **实现健康检查** - 自动检测和移除失效密钥
3. **负载均衡优化** - 优先使用高质量密钥

========================================
📈 密钥使用模式分析
========================================

## 轮换模式
- 系统按顺序轮换使用 17 个密钥
- 遇到失败时自动切换到下一个密钥
- 最多重试 3 次

## 失败处理
- 400 错误: 切换密钥重试
- 403 错误: 切换密钥重试
- 连续 3 次失败: 返回 500 错误

## 优化空间
- 跳过已知的被暂停密钥
- 优先使用高成功率的密钥
- 实现智能重试策略

========================================
🔔 监控指标
========================================

## 关键指标
- **当前密钥数量**: 17 个
- **估计有效密钥**: 13 个
- **当前成功率**: ~25%
- **目标成功率**: >80%

## 告警阈值
- 密钥数量 < 10 个时告警
- 成功率 < 70% 时告警
- 新的 CONSUMER_SUSPENDED 错误时告警

========================================
总结
========================================

**当前状态**: 17 个 API 密钥，包含 4 个被暂停的密钥
**主要问题**: additionalProperties 错误 + 被暂停密钥
**解决方案**: 重启服务 + 清理被暂停密钥
**预期结果**: 成功率从 25% 提升至 80%+

**立即执行**: `py gemini_balance_service.py restart`
