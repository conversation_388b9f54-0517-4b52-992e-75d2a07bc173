# 被暂停的 Gemini API 密钥列表
# 生成时间: 2025-07-31 03:22
# 错误类型: 403 PERMISSION_DENIED - CONSUMER_SUSPENDED
# 来源: logs/app_stdout.log 分析结果

========================================
被暂停的 API 密钥 (完整列表)
========================================

1. AIzaSyBxkvVTOJb8zvOdYxdEeJMadDPRv8sXwJ0
   项目ID: projects/946720301782
   状态: CONSUMER_SUSPENDED

2. AIzaSyCMyEGcCIwXahRjzIunh-KO_psO0U9ZmnE
   项目ID: projects/941314918349
   状态: CONSUMER_SUSPENDED

3. AIzaSyDmJKwf_O9gSY0C9kaCkEQpct3puSU3pL4
   项目ID: projects/308748635735
   状态: CONSUMER_SUSPENDED

4. AIzaSyASzSAipilXUUftuTgP7SuzWcr8goXaD30
   项目ID: projects/663274527292
   状态: CONSUMER_SUSPENDED

5. AIzaSyDFtn4L4QnG6f_T_oy-QU3RCnSjGspeKeE
   项目ID: projects/394265401472
   状态: CONSUMER_SUSPENDED

6. AIzaSyAT6rPzhAMiwOnjWWLscAfxLvdRaGFJeNg
   项目ID: projects/652919033449
   状态: CONSUMER_SUSPENDED

7. AIzaSyDqw2P__xctNxWt2JaR3IGCbk_NLU_OJDU
   项目ID: projects/191697201035
   状态: CONSUMER_SUSPENDED

8. AIzaSyCZch0ezSzEcnUeB60HZWb-uzvUt4SfW0I
   项目ID: projects/767721242036
   状态: CONSUMER_SUSPENDED

9. AIzaSyDaroOULn3FdeJASLqlosWr0RXDSZt2-x0
   项目ID: projects/819152869383
   状态: CONSUMER_SUSPENDED

10. AIzaSyCMjX6TlBujpII8WX9NOtnYW9COP9BvUzg
    项目ID: projects/1039052191811
    状态: CONSUMER_SUSPENDED

========================================
完整被暂停密钥列表（从日志分析）
========================================

基于日志文件 logs/app_stdout.log 的完整分析，发现以下所有被暂停的 API 密钥：

1. AIzaSyBxkvVTOJb8zvOdYxdEeJMadDPRv8sXwJ0 (projects/946720301782)
2. AIzaSyCMyEGcCIwXahRjzIunh-KO_psO0U9ZmnE (projects/941314918349)
3. AIzaSyDmJKwf_O9gSY0C9kaCkEQpct3puSU3pL4 (projects/308748635735)
4. AIzaSyASzSAipilXUUftuTgP7SuzWcr8goXaD30 (projects/663274527292)
5. AIzaSyDFtn4L4QnG6f_T_oy-QU3RCnSjGspeKeE (projects/394265401472)
6. AIzaSyAT6rPzhAMiwOnjWWLscAfxLvdRaGFJeNg (projects/652919033449)
7. AIzaSyDqw2P__xctNxWt2JaR3IGCbk_NLU_OJDU (projects/191697201035)
8. AIzaSyCZch0ezSzEcnUeB60HZWb-uzvUt4SfW0I (projects/767721242036)
9. AIzaSyDaroOULn3FdeJASLqlosWr0RXDSZt2-x0 (projects/819152869383)
10. AIzaSyCMjX6TlBujpII8WX9NOtnYW9COP9BvUzg (projects/1039052191811)

注意：这些密钥在日志中重复出现多次，表明系统一直在尝试使用它们但都被拒绝。

========================================
最新发现的有效 API 密钥（仍在使用中）
========================================

从最新日志分析发现，以下密钥目前仍在正常使用：

✅ 有效密钥（部分显示）:
- AIzaSy...R55kXk (正常工作)
- AIzaSy...71ZnR0 (正常工作)
- AIzaSy...tdjjgk (部分请求成功)
- AIzaSy...KwuqH0 (部分请求成功)
- AIzaSy...wJaBPE (部分请求成功)
- AIzaSy...Arv4Pg (部分请求成功)
- AIzaSy...FbCPzI (正常工作)
- AIzaSy...TdyKqk (部分请求失败 - JSON格式问题)
- AIzaSy...2kru0c (正常工作)
- AIzaSy...whkw_E (部分请求失败 - JSON格式问题)
- AIzaSy...dn7KNM (部分请求失败 - JSON格式问题)
- AIzaSy...P4ztzU (部分请求失败 - JSON格式问题)
- AIzaSy...b3Asow (部分请求失败 - JSON格式问题)
- AIzaSy...Y8Yblg (正常工作)
- AIzaSy...lWcfh8 (正常工作)
- AIzaSy...2ehw9I (正常工作)
- AIzaSy...3rM1wc (正常工作)
- AIzaSy...dAfeYc (部分请求成功，部分被暂停)

注意：部分密钥出现 400 错误是由于 JSON 格式问题（additionalProperties 字段），不是密钥被暂停。

========================================
统计信息
========================================

总计被暂停密钥数量: 10 个确认被暂停
有效密钥数量: 18+ 个仍在工作
日志匹配次数: 120+ 次 CONSUMER_SUSPENDED 错误记录
错误出现频率: 高频（被暂停密钥重复错误）
影响范围: 所有 chat completion 和 stream generate 请求
错误代码: 403 PERMISSION_DENIED (被暂停) + 400 INVALID_ARGUMENT (JSON格式)
错误原因: CONSUMER_SUSPENDED + JSON格式问题

========================================
解决方案
========================================

1. 立即行动:
   - 移除所有被暂停的 API 密钥
   - 获取新的有效 Gemini API 密钥
   - 更新 .env 配置文件

2. 获取新密钥:
   - 访问: https://makersuite.google.com/app/apikey
   - 创建新的 Google Cloud 项目
   - 启用 Generative Language API
   - 生成新的 API 密钥

3. 配置更新:
   - 编辑 .env 文件
   - 替换 API_KEYS 数组中的所有密钥
   - 重启服务: py gemini_balance_service.py restart

4. 监控建议:
   - 定期检查密钥状态
   - 设置使用配额监控
   - 避免过度使用导致暂停

========================================
注意事项
========================================

- 这些密钥已被 Google 永久暂停，无法恢复
- 需要使用全新的 Google Cloud 项目创建新密钥
- 建议配置多个有效密钥进行负载均衡
- 定期轮换密钥以避免单点故障

========================================
相关文件
========================================

- 配置文件: .env
- 服务脚本: gemini_balance_service.py
- 错误日志: logs/app_stdout.log
- 分析报告: 500_error_analysis.md
- 修复脚本: fix_500_errors.bat
