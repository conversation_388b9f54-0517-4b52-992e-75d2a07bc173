#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API 测试脚本
测试 API 是否返回空响应
"""

import requests
import json
import time

def test_api():
    """测试 API 调用"""
    url = "http://127.0.0.1:8001/v1/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer 1"
    }
    
    payload = {
        "model": "gemini-2.5-flash",
        "messages": [
            {
                "role": "user",
                "content": "Hello, this is a test message. Please respond with some text."
            }
        ]
    }
    
    print("🔄 测试 API 调用...")
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("-" * 50)
    
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        end_time = time.time()
        
        print(f"⏱️ 响应时间: {end_time - start_time:.2f} 秒")
        print(f"📊 状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print("✅ JSON 解析成功")
                print(f"📄 完整响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 检查响应内容
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    choice = response_data["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        content = choice["message"]["content"]
                        print(f"💬 响应内容: '{content}'")
                        print(f"📏 内容长度: {len(content)} 字符")
                        
                        if not content or content.strip() == "":
                            print("❌ 响应内容为空！")
                            return False
                        elif content == "暂无返回":
                            print("❌ 响应为默认文本 '暂无返回'！")
                            return False
                        else:
                            print("✅ 响应内容正常")
                            return True
                    else:
                        print("❌ 响应结构异常：缺少 message.content")
                        return False
                else:
                    print("❌ 响应结构异常：缺少 choices")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                print(f"📄 原始响应: {response.text}")
                return False
        else:
            print(f"❌ HTTP 错误: {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 API 空响应测试工具")
    print("=" * 60)
    
    # 进行 3 次测试
    results = []
    for i in range(3):
        print(f"\n🔄 测试 {i+1}/3:")
        result = test_api()
        results.append(result)
        
        if i < 2:  # 不是最后一次测试
            print("⏳ 等待 2 秒后进行下一次测试...")
            time.sleep(2)
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    success_count = sum(results)
    total_count = len(results)
    
    for i, result in enumerate(results, 1):
        status = "✅ 成功" if result else "❌ 失败"
        print(f"测试 {i}: {status}")
    
    print(f"\n📈 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == 0:
        print("🚨 所有测试都失败了！API 存在严重问题。")
    elif success_count < total_count:
        print("⚠️ 部分测试失败，API 不稳定。")
    else:
        print("🎉 所有测试都成功！API 工作正常。")

if __name__ == "__main__":
    main()
