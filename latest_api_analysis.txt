# 最新 API 密钥状态分析报告
# 生成时间: 2025-07-31 03:22
# 基于最新日志: logs/app_stdout.log (9004行)

========================================
关键发现
========================================

🔍 深度日志分析结果：
- 总计 API 密钥使用记录: 107 次
- CONSUMER_SUSPENDED 错误: 120 次
- 有效密钥数量: 18+ 个
- 被暂停密钥数量: 10 个确认

========================================
当前状态分类
========================================

✅ 正常工作的密钥 (高成功率):
1. AIzaSy...R55kXk - 多次成功请求
2. AIzaSy...71ZnR0 - 多次成功请求  
3. AIzaSy...Y8Yblg - 多次成功请求
4. AIzaSy...lWcfh8 - 多次成功请求
5. AIzaSy...2ehw9I - 多次成功请求
6. AIzaSy...3rM1wc - 多次成功请求
7. AIzaSy...2kru0c - 多次成功请求
8. AIzaSy...FbCPzI - 多次成功请求
9. AIzaSy...dAfeYc - 部分成功（有时被暂停）

⚠️ 部分工作的密钥 (混合结果):
1. AIzaSy...tdjjgk - 成功 + 400错误
2. AIzaSy...KwuqH0 - 成功 + 400错误
3. AIzaSy...wJaBPE - 成功 + 400错误
4. AIzaSy...Arv4Pg - 成功 + 400错误
5. AIzaSy...TdyKqk - 400错误 (JSON格式问题)
6. AIzaSy...whkw_E - 400错误 (JSON格式问题)
7. AIzaSy...dn7KNM - 400错误 (JSON格式问题)
8. AIzaSy...P4ztzU - 400错误 (JSON格式问题)
9. AIzaSy...b3Asow - 400错误 (JSON格式问题)

🚫 确认被暂停的密钥 (403错误):
1. AIzaSyBxkvVTOJb8zvOdYxdEeJMadDPRv8sXwJ0
2. AIzaSyCMyEGcCIwXahRjzIunh-KO_psO0U9ZmnE
3. AIzaSyDmJKwf_O9gSY0C9kaCkEQpct3puSU3pL4
4. AIzaSyASzSAipilXUUftuTgP7SuzWcr8goXaD30
5. AIzaSyDFtn4L4QnG6f_T_oy-QU3RCnSjGspeKeE
6. AIzaSyAT6rPzhAMiwOnjWWLscAfxLvdRaGFJeNg
7. AIzaSyDqw2P__xctNxWt2JaR3IGCbk_NLU_OJDU
8. AIzaSyCZch0ezSzEcnUeB60HZWb-uzvUt4SfW0I
9. AIzaSyDaroOULn3FdeJASLqlosWr0RXDSZt2-x0
10. AIzaSyCMjX6TlBujpII8WX9NOtnYW9COP9BvUzg

========================================
错误类型分析
========================================

📊 错误分布:
- 403 CONSUMER_SUSPENDED: 120 次 (被暂停密钥)
- 400 INVALID_ARGUMENT: 50+ 次 (JSON格式问题)
- 200 SUCCESS: 30+ 次 (成功请求)

🔧 400 错误详情:
主要原因: "Unknown name 'additionalProperties' at 'tools[0].function_declarations[0].parameters'"
影响: 部分有效密钥也会因为请求格式问题返回400错误

========================================
系统行为分析
========================================

🔄 负载均衡机制:
- 系统在 28 个密钥间轮换
- 遇到失败自动切换到下一个密钥
- 最多重试 3 次

📈 成功率估算:
- 完全有效密钥: ~32% (9/28)
- 部分有效密钥: ~32% (9/28) 
- 被暂停密钥: ~36% (10/28)
- 总体成功率: ~40-50%

========================================
间歇性500错误原因
========================================

1. **主要原因**: 密钥轮换到被暂停的密钥时返回403错误
2. **次要原因**: JSON格式问题导致400错误
3. **系统表现**: 
   - 使用有效密钥时 → 200 成功
   - 使用被暂停密钥时 → 500 错误
   - 使用有效密钥但JSON格式错误时 → 500 错误

========================================
解决方案优先级
========================================

🚨 立即行动 (高优先级):
1. 移除 10 个被暂停的密钥
2. 修复 JSON 格式问题 (additionalProperties 字段)
3. 重启服务应用修复

⚡ 短期优化 (中优先级):
1. 增加更多有效的 API 密钥
2. 实现密钥健康检查机制
3. 优化错误处理和重试逻辑

🔧 长期改进 (低优先级):
1. 实现密钥状态监控
2. 自动密钥轮换和更新
3. 负载均衡算法优化

========================================
预期效果
========================================

移除被暂停密钥后:
- 500 错误率预计下降 60-70%
- 整体成功率提升至 80-90%
- 响应时间更稳定 (减少重试)

修复 JSON 格式问题后:
- 400 错误完全消除
- 成功率进一步提升至 95%+
- 用户体验显著改善

========================================
监控建议
========================================

📊 关键指标:
- API 成功率 (目标: >95%)
- 平均响应时间 (目标: <2秒)
- 错误率分布 (403/400/500)
- 密钥使用分布

🔔 告警设置:
- 成功率低于 90% 时告警
- 单个密钥连续失败 5 次时告警
- 新的 CONSUMER_SUSPENDED 错误时告警
