#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 API 服务器返回空响应问题的脚本
主要解决：
1. JSON 格式中的 additionalProperties 字段问题 (已修复)
2. 数据库配置与 .env 文件不同步问题
"""

import os
import sqlite3
import json
from pathlib import Path

def check_database_config():
    """检查数据库中的 API_KEYS 配置"""
    db_path = "default_db"
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询 API_KEYS 配置
        cursor.execute("SELECT key, value FROM settings WHERE key = 'API_KEYS'")
        result = cursor.fetchone()
        
        if result:
            key, value = result
            try:
                api_keys = json.loads(value)
                print(f"📊 数据库中的 API_KEYS 数量: {len(api_keys)}")
                print(f"📋 前3个密钥预览: {[key[:20] + '...' for key in api_keys[:3]]}")
                return api_keys
            except json.JSONDecodeError:
                print(f"❌ 无法解析数据库中的 API_KEYS: {value}")
                return None
        else:
            print("❌ 数据库中未找到 API_KEYS 配置")
            return None
            
    except Exception as e:
        print(f"❌ 查询数据库失败: {e}")
        return None
    finally:
        if 'conn' in locals():
            conn.close()

def check_env_config():
    """检查 .env 文件中的 API_KEYS 配置"""
    env_path = Path(".env")
    
    if not env_path.exists():
        print("❌ .env 文件不存在")
        return None
    
    try:
        with open(env_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 API_KEYS 行
        for line in content.split('\n'):
            if line.strip().startswith('API_KEYS='):
                value = line.split('=', 1)[1].strip()
                try:
                    api_keys = json.loads(value)
                    print(f"📊 .env 文件中的 API_KEYS 数量: {len(api_keys)}")
                    print(f"📋 密钥预览: {api_keys}")
                    return api_keys
                except json.JSONDecodeError:
                    print(f"❌ 无法解析 .env 中的 API_KEYS: {value}")
                    return None
        
        print("❌ .env 文件中未找到 API_KEYS 配置")
        return None
        
    except Exception as e:
        print(f"❌ 读取 .env 文件失败: {e}")
        return None

def update_database_config(new_api_keys):
    """更新数据库中的 API_KEYS 配置"""
    db_path = "default_db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 更新 API_KEYS 配置
        api_keys_json = json.dumps(new_api_keys)
        cursor.execute(
            "UPDATE settings SET value = ? WHERE key = 'API_KEYS'",
            (api_keys_json,)
        )
        
        if cursor.rowcount == 0:
            # 如果没有更新任何行，则插入新记录
            cursor.execute(
                "INSERT INTO settings (key, value) VALUES ('API_KEYS', ?)",
                (api_keys_json,)
            )
        
        conn.commit()
        print(f"✅ 数据库配置已更新，新的密钥数量: {len(new_api_keys)}")
        return True
        
    except Exception as e:
        print(f"❌ 更新数据库失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 API 服务器空响应问题修复工具")
    print("=" * 60)
    
    print("\n1. 检查当前配置状态...")
    
    # 检查 .env 文件配置
    print("\n📁 检查 .env 文件配置:")
    env_keys = check_env_config()
    
    # 检查数据库配置
    print("\n🗄️ 检查数据库配置:")
    db_keys = check_database_config()
    
    # 比较配置
    print("\n🔍 配置对比分析:")
    if env_keys and db_keys:
        if len(env_keys) != len(db_keys):
            print(f"⚠️  配置不一致: .env({len(env_keys)}个) vs 数据库({len(db_keys)}个)")
            
            # 检查是否包含占位符
            if env_keys == ["your_valid_gemini_api_key_here"]:
                print("⚠️  .env 文件包含占位符，需要添加真实的 API 密钥")
            else:
                print("🔄 准备同步 .env 配置到数据库...")
                if update_database_config(env_keys):
                    print("✅ 配置同步完成")
                else:
                    print("❌ 配置同步失败")
        else:
            print("✅ 配置一致")
    
    print("\n2. 检查代码修复状态...")
    
    # 检查 additionalProperties 修复
    openai_file = Path("app/service/chat/openai_chat_service.py")
    gemini_file = Path("app/service/chat/gemini_chat_service.py")
    
    fixed_count = 0
    for file_path in [openai_file, gemini_file]:
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if '"additionalProperties"' in content:
                    print(f"✅ {file_path.name} 已修复 additionalProperties 问题")
                    fixed_count += 1
                else:
                    print(f"❌ {file_path.name} 未修复 additionalProperties 问题")
        else:
            print(f"❌ 文件不存在: {file_path}")
    
    print(f"\n📊 修复状态: {fixed_count}/2 个文件已修复")
    
    print("\n3. 修复建议:")
    
    if env_keys == ["your_valid_gemini_api_key_here"]:
        print("🚨 紧急: 需要添加真实的 Gemini API 密钥到 .env 文件")
        print("   访问: https://makersuite.google.com/app/apikey")
    
    if fixed_count < 2:
        print("🔧 需要手动修复 additionalProperties 问题")
    
    print("\n4. 下一步操作:")
    print("   1. 确保 .env 文件包含有效的 API 密钥")
    print("   2. 重启服务: py gemini_balance_service.py restart")
    print("   3. 监控日志: tail -f logs/app_stdout.log")
    print("   4. 测试 API 调用确认修复效果")
    
    print("\n" + "=" * 60)
    print("🎯 预期效果: 空响应问题完全解决，API 成功率提升至 95%+")
    print("=" * 60)

if __name__ == "__main__":
    main()
