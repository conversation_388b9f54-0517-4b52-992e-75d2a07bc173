#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库重置验证脚本
确认数据库文件已删除，系统将重新从 .env 文件加载配置
"""

import os
import json
from pathlib import Path

def check_database_status():
    """检查数据库状态"""
    print("=" * 60)
    print("🗄️ 数据库重置验证")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    db_path = Path("./data/default_db")
    
    if db_path.exists():
        print("❌ 数据库文件仍然存在")
        print(f"   路径: {db_path.absolute()}")
        print("   建议: 手动删除该文件")
        return False
    else:
        print("✅ 数据库文件已成功删除")
        print(f"   路径: {db_path.absolute()}")
        print("   状态: 不存在")
    
    return True

def check_env_config():
    """检查 .env 文件配置"""
    print("\n📁 检查 .env 文件配置:")
    
    env_path = Path(".env")
    if not env_path.exists():
        print("❌ .env 文件不存在")
        return None
    
    try:
        with open(env_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 API_KEYS 行
        for line in content.split('\n'):
            if line.strip().startswith('API_KEYS='):
                value = line.split('=', 1)[1].strip()
                try:
                    api_keys = json.loads(value)
                    print(f"✅ .env 文件中的 API_KEYS 数量: {len(api_keys)}")
                    
                    # 检查是否包含占位符
                    if api_keys == ["your_valid_gemini_api_key_here"]:
                        print("⚠️  包含占位符，需要添加真实的 API 密钥")
                        return "placeholder"
                    else:
                        print(f"✅ 包含 {len(api_keys)} 个真实的 API 密钥")
                        # 显示前几个密钥的前缀
                        for i, key in enumerate(api_keys[:3], 1):
                            print(f"   {i}. {key[:20]}...")
                        if len(api_keys) > 3:
                            print(f"   ... 还有 {len(api_keys) - 3} 个密钥")
                        return api_keys
                        
                except json.JSONDecodeError:
                    print(f"❌ 无法解析 .env 中的 API_KEYS: {value}")
                    return None
        
        print("❌ .env 文件中未找到 API_KEYS 配置")
        return None
        
    except Exception as e:
        print(f"❌ 读取 .env 文件失败: {e}")
        return None

def check_suspended_keys(api_keys):
    """检查是否还包含被暂停的密钥"""
    if not api_keys or api_keys == "placeholder":
        return
    
    print("\n🚫 检查被暂停的密钥:")
    
    # 已知被暂停的密钥
    suspended_keys = [
        "AIzaSyBxkvVTOJb8zvOdYxdEeJMadDPRv8sXwJ0",
        "AIzaSyCMyEGcCIwXahRjzIunh-KO_psO0U9ZmnE",
        "AIzaSyDmJKwf_O9gSY0C9kaCkEQpct3puSU3pL4",
        "AIzaSyASzSAipilXUUftuTgP7SuzWcr8goXaD30",
        "AIzaSyDFtn4L4QnG6f_T_oy-QU3RCnSjGspeKeE",
        "AIzaSyAT6rPzhAMiwOnjWWLscAfxLvdRaGFJeNg",
        "AIzaSyDqw2P__xctNxWt2JaR3IGCbk_NLU_OJDU",
        "AIzaSyCZch0ezSzEcnUeB60HZWb-uzvUt4SfW0I",
        "AIzaSyDaroOULn3FdeJASLqlosWr0RXDSZt2-x0",
        "AIzaSyCMjX6TlBujpII8WX9NOtnYW9COP9BvUzg"
    ]
    
    found_suspended = []
    for key in api_keys:
        if key in suspended_keys:
            found_suspended.append(key)
    
    if found_suspended:
        print(f"❌ 发现 {len(found_suspended)} 个被暂停的密钥:")
        for key in found_suspended:
            print(f"   - {key}")
        print("   建议: 从 .env 文件中移除这些密钥")
    else:
        print("✅ 未发现被暂停的密钥")

def main():
    """主函数"""
    # 检查数据库状态
    db_deleted = check_database_status()
    
    # 检查 .env 配置
    api_keys = check_env_config()
    
    # 检查被暂停的密钥
    check_suspended_keys(api_keys)
    
    print("\n" + "=" * 60)
    print("📋 重置状态总结")
    print("=" * 60)
    
    if db_deleted:
        print("✅ 数据库文件已删除")
    else:
        print("❌ 数据库文件删除失败")
    
    if api_keys == "placeholder":
        print("⚠️  .env 文件包含占位符")
    elif api_keys:
        print(f"✅ .env 文件包含 {len(api_keys)} 个 API 密钥")
    else:
        print("❌ .env 文件配置有问题")
    
    print("\n🚀 下一步操作:")
    if db_deleted:
        print("1. 确保 .env 文件包含有效的 API 密钥")
        print("2. 重启服务: py gemini_balance_service.py restart")
        print("3. 系统将自动从 .env 文件重新创建数据库")
        print("4. 监控日志确认只使用 .env 中的密钥")
    else:
        print("1. 手动删除数据库文件: del data\\default_db")
        print("2. 然后重启服务")
    
    print("\n💡 提示:")
    print("- 重启后系统会显示: 'KeyManager instance created with X API keys'")
    print("- X 应该等于 .env 文件中的密钥数量")
    print("- 不应再看到被暂停密钥的错误")

if __name__ == "__main__":
    main()
