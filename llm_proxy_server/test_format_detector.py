#!/usr/bin/env python3
"""
格式检测服务测试脚本
用于验证智能格式检测功能是否正常工作
"""

import asyncio
import json
import sys
from typing import Dict, Any
from fastapi import Request
from fastapi.testclient import TestClient
from unittest.mock import Mock

def create_mock_request(
    method: str = "POST",
    path: str = "/",
    headers: Dict[str, str] = None,
    body: Dict[str, Any] = None,
    query_params: Dict[str, str] = None
) -> Mock:
    """创建模拟请求对象"""
    mock_request = Mock(spec=Request)
    mock_request.method = method
    
    # 模拟 URL
    mock_url = Mock()
    mock_url.path = path
    mock_request.url = mock_url
    
    # 模拟请求头
    mock_request.headers = headers or {}
    
    # 模拟查询参数
    mock_request.query_params = query_params or {}
    
    # 模拟请求体
    if body:
        body_bytes = json.dumps(body).encode()
        mock_request._body = body_bytes
        
        async def mock_body():
            return body_bytes
        mock_request.body = mock_body
    else:
        async def mock_empty_body():
            return b""
        mock_request.body = mock_empty_body
    
    return mock_request

def test_imports():
    """测试导入"""
    print("Testing imports...")
    try:
        from service.format_detector import FormatDetector, CachedFormatDetector
        from models.conversion_models import LLMFormat, FormatDetectionResult
        print("✓ Format detector imports successful")
        return True
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_detector_initialization():
    """测试检测器初始化"""
    print("\nTesting detector initialization...")
    try:
        from service.format_detector import FormatDetector, CachedFormatDetector
        
        # 测试基础检测器
        detector = FormatDetector()
        print("✓ Basic detector initialized")
        
        # 测试缓存检测器
        cached_detector = CachedFormatDetector(cache_size=100)
        print("✓ Cached detector initialized")
        
        # 测试统计信息
        stats = detector.get_detection_stats()
        print(f"✓ Detection stats: {len(stats['supported_formats'])} formats supported")
        
        return True
    except Exception as e:
        print(f"✗ Detector initialization failed: {e}")
        return False

async def test_gemini_detection():
    """测试 Gemini 格式检测"""
    print("\nTesting Gemini format detection...")
    try:
        from service.format_detector import FormatDetector
        from models.conversion_models import LLMFormat
        
        detector = FormatDetector()
        
        # 测试 Gemini 路径检测
        test_cases = [
            {
                "name": "Gemini generateContent",
                "request": create_mock_request(
                    method="POST",
                    path="/v1beta/models/gemini-pro:generateContent",
                    headers={"x-goog-api-key": "test-key"},
                    body={
                        "contents": [
                            {
                                "role": "user",
                                "parts": [{"text": "Hello"}]
                            }
                        ],
                        "generationConfig": {"temperature": 0.7}
                    }
                )
            },
            {
                "name": "Gemini streamGenerateContent",
                "request": create_mock_request(
                    method="POST",
                    path="/v1beta/models/gemini-pro:streamGenerateContent",
                    body={
                        "contents": [{"role": "user", "parts": [{"text": "Hello"}]}]
                    }
                )
            },
            {
                "name": "Gemini models list",
                "request": create_mock_request(
                    method="GET",
                    path="/v1beta/models"
                )
            }
        ]
        
        for test_case in test_cases:
            result = await detector.detect_format(test_case["request"])
            if result.detected_format == LLMFormat.GEMINI:
                print(f"✓ {test_case['name']}: {result.detected_format} (confidence: {result.confidence:.2f})")
            else:
                print(f"✗ {test_case['name']}: Expected Gemini, got {result.detected_format}")
                return False
        
        return True
    except Exception as e:
        print(f"✗ Gemini detection test failed: {e}")
        return False

async def test_openai_detection():
    """测试 OpenAI 格式检测"""
    print("\nTesting OpenAI format detection...")
    try:
        from service.format_detector import FormatDetector
        from models.conversion_models import LLMFormat
        
        detector = FormatDetector()
        
        test_cases = [
            {
                "name": "OpenAI chat completions",
                "request": create_mock_request(
                    method="POST",
                    path="/v1/chat/completions",
                    headers={"authorization": "Bearer sk-test123"},
                    body={
                        "model": "gpt-3.5-turbo",
                        "messages": [
                            {"role": "user", "content": "Hello"}
                        ],
                        "temperature": 0.7
                    }
                )
            },
            {
                "name": "OpenAI embeddings",
                "request": create_mock_request(
                    method="POST",
                    path="/v1/embeddings",
                    body={
                        "model": "text-embedding-ada-002",
                        "input": "Hello world"
                    }
                )
            },
            {
                "name": "OpenAI models",
                "request": create_mock_request(
                    method="GET",
                    path="/v1/models"
                )
            }
        ]
        
        for test_case in test_cases:
            result = await detector.detect_format(test_case["request"])
            if result.detected_format == LLMFormat.OPENAI:
                print(f"✓ {test_case['name']}: {result.detected_format} (confidence: {result.confidence:.2f})")
            else:
                print(f"✗ {test_case['name']}: Expected OpenAI, got {result.detected_format}")
                return False
        
        return True
    except Exception as e:
        print(f"✗ OpenAI detection test failed: {e}")
        return False

async def test_claude_detection():
    """测试 Claude 格式检测"""
    print("\nTesting Claude format detection...")
    try:
        from service.format_detector import FormatDetector
        from models.conversion_models import LLMFormat
        
        detector = FormatDetector()
        
        test_cases = [
            {
                "name": "Claude messages",
                "request": create_mock_request(
                    method="POST",
                    path="/v1/messages",
                    headers={"x-api-key": "test-key", "anthropic-version": "2023-06-01"},
                    body={
                        "model": "claude-3-sonnet-20240229",
                        "messages": [
                            {"role": "user", "content": "Hello"}
                        ],
                        "max_tokens": 1000,
                        "system": "You are a helpful assistant"
                    }
                )
            }
        ]
        
        for test_case in test_cases:
            result = await detector.detect_format(test_case["request"])
            if result.detected_format in [LLMFormat.CLAUDE, LLMFormat.ANTHROPIC]:
                print(f"✓ {test_case['name']}: {result.detected_format} (confidence: {result.confidence:.2f})")
            else:
                print(f"✗ {test_case['name']}: Expected Claude/Anthropic, got {result.detected_format}")
                return False
        
        return True
    except Exception as e:
        print(f"✗ Claude detection test failed: {e}")
        return False

async def test_cohere_detection():
    """测试 Cohere 格式检测"""
    print("\nTesting Cohere format detection...")
    try:
        from service.format_detector import FormatDetector
        from models.conversion_models import LLMFormat
        
        detector = FormatDetector()
        
        request = create_mock_request(
            method="POST",
            path="/v1/generate",
            body={
                "model": "command",
                "message": "Hello",
                "chat_history": [
                    {"user_name": "User", "message": "Hi"}
                ]
            }
        )
        
        result = await detector.detect_format(request)
        if result.detected_format == LLMFormat.COHERE:
            print(f"✓ Cohere detection: {result.detected_format} (confidence: {result.confidence:.2f})")
            return True
        else:
            print(f"✗ Expected Cohere, got {result.detected_format}")
            return False
        
    except Exception as e:
        print(f"✗ Cohere detection test failed: {e}")
        return False

async def test_cache_functionality():
    """测试缓存功能"""
    print("\nTesting cache functionality...")
    try:
        from service.format_detector import CachedFormatDetector
        
        detector = CachedFormatDetector(cache_size=10)
        
        # 创建测试请求
        request = create_mock_request(
            method="POST",
            path="/v1/chat/completions",
            body={"model": "gpt-3.5-turbo", "messages": []}
        )
        
        # 第一次检测（缓存未命中）
        result1 = await detector.detect_format(request)
        stats1 = detector.get_cache_stats()
        
        # 第二次检测（缓存命中）
        result2 = await detector.detect_format(request)
        stats2 = detector.get_cache_stats()
        
        if stats2["cache_hits"] > stats1["cache_hits"]:
            print("✓ Cache functionality working")
            print(f"✓ Cache stats: {stats2['cache_hits']} hits, {stats2['cache_misses']} misses")
            return True
        else:
            print("✗ Cache not working as expected")
            return False
        
    except Exception as e:
        print(f"✗ Cache functionality test failed: {e}")
        return False

async def test_unknown_format_handling():
    """测试未知格式处理"""
    print("\nTesting unknown format handling...")
    try:
        from service.format_detector import FormatDetector
        from models.conversion_models import LLMFormat
        
        detector = FormatDetector()
        
        # 创建无法识别的请求
        request = create_mock_request(
            method="POST",
            path="/unknown/api/endpoint",
            body={"unknown_field": "unknown_value"}
        )
        
        result = await detector.detect_format(request)
        
        if result.detected_format == LLMFormat.UNKNOWN:
            print(f"✓ Unknown format handled correctly: confidence {result.confidence:.2f}")
            return True
        else:
            print(f"✗ Expected UNKNOWN, got {result.detected_format}")
            return False
        
    except Exception as e:
        print(f"✗ Unknown format handling test failed: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 70)
    print("LLM Proxy Server - Format Detection Test")
    print("=" * 70)
    
    tests = [
        ("Import Test", test_imports),
        ("Detector Initialization", test_detector_initialization),
        ("Gemini Detection", test_gemini_detection),
        ("OpenAI Detection", test_openai_detection),
        ("Claude Detection", test_claude_detection),
        ("Cohere Detection", test_cohere_detection),
        ("Cache Functionality", test_cache_functionality),
        ("Unknown Format Handling", test_unknown_format_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All format detection tests passed! Format detector is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
