"""
配置管理模块
基于 pydantic_settings 的配置管理，复用现有项目的配置模式
"""

from typing import List, Dict, Any, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class ProxySettings(BaseSettings):
    """代理服务器配置类"""
    
    # 代理服务器配置
    PROXY_HOST: str = Field(default="0.0.0.0", description="代理服务监听地址")
    PROXY_PORT: int = Field(default=8002, description="代理服务端口")
    
    # 目标服务器配置
    TARGET_HOST: str = Field(default="127.0.0.1", description="目标服务地址")
    TARGET_PORT: int = Field(default=8001, description="目标服务端口")
    TARGET_BASE_URL: str = Field(default="", description="目标服务基础URL，自动生成")
    
    # LiteLLM 配置
    LITELLM_API_KEY: str = Field(default="", description="LiteLLM API 密钥")
    LITELLM_BASE_URL: str = Field(default="", description="LiteLLM 基础 URL")
    LITELLM_CONFIG: Dict[str, Any] = Field(default_factory=dict, description="LiteLLM 额外配置")
    
    # 性能配置
    MAX_CONCURRENT_REQUESTS: int = Field(default=100, description="最大并发请求数")
    REQUEST_TIMEOUT: int = Field(default=300, description="请求超时时间（秒）")
    CONNECTION_POOL_SIZE: int = Field(default=20, description="连接池大小")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    
    # 代理配置（可选）
    PROXIES: List[str] = Field(default_factory=list, description="HTTP代理列表")
    USE_PROXY: bool = Field(default=False, description="是否使用代理")
    
    # 安全配置
    ALLOWED_ORIGINS: List[str] = Field(default_factory=lambda: ["*"], description="允许的跨域来源")
    API_KEY_HEADER: str = Field(default="Authorization", description="API密钥请求头名称")
    
    # 格式检测配置
    FORMAT_DETECTION_CONFIDENCE_THRESHOLD: float = Field(
        default=0.5, description="格式检测置信度阈值"
    )
    ENABLE_FORMAT_CACHE: bool = Field(default=True, description="是否启用格式检测缓存")
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=True, description="是否启用性能监控")
    HEALTH_CHECK_INTERVAL: int = Field(default=30, description="健康检查间隔（秒）")
    
    @validator("TARGET_BASE_URL", always=True)
    def generate_target_base_url(cls, v, values):
        """自动生成目标服务基础URL"""
        if not v:
            host = values.get("TARGET_HOST", "127.0.0.1")
            port = values.get("TARGET_PORT", 8001)
            return f"http://{host}:{port}"
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"日志级别必须是以下之一: {valid_levels}")
        return v.upper()
    
    @validator("PROXY_PORT", "TARGET_PORT")
    def validate_port(cls, v):
        """验证端口号"""
        if not (1 <= v <= 65535):
            raise ValueError("端口号必须在 1-65535 范围内")
        return v
    
    class Config:
        """配置类设置"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"  # 忽略额外的环境变量


# 创建全局配置实例
settings = ProxySettings()


def get_settings() -> ProxySettings:
    """获取配置实例"""
    return settings


def reload_settings() -> ProxySettings:
    """重新加载配置"""
    global settings
    settings = ProxySettings()
    return settings
