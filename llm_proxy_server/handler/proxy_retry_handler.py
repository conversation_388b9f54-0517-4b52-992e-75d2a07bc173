"""
代理重试处理器
复用现有项目的重试处理模式，为代理转发提供重试功能
"""

import asyncio
from functools import wraps
from typing import Callable, TypeVar, Any, Dict, Optional

from config.settings import settings
from log.logger import get_proxy_forwarder_logger

T = TypeVar("T")
logger = get_proxy_forwarder_logger()


class ProxyRetryHandler:
    """代理重试处理装饰器，基于现有项目的 RetryHandler"""
    
    def __init__(self, max_retries: Optional[int] = None, retry_delay: float = 1.0):
        """
        初始化重试处理器
        
        Args:
            max_retries: 最大重试次数，默认使用配置值
            retry_delay: 重试延迟（秒）
        """
        self.max_retries = max_retries or getattr(settings, 'MAX_RETRIES', 3)
        self.retry_delay = retry_delay
        
        logger.info(f"Proxy retry handler initialized: max_retries={self.max_retries}, delay={self.retry_delay}s")
    
    def __call__(self, func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            last_exception = None
            
            for attempt in range(self.max_retries):
                attempt_num = attempt + 1
                
                try:
                    logger.debug(f"Executing {func.__name__}, attempt {attempt_num}/{self.max_retries}")
                    result = await func(*args, **kwargs)
                    
                    if attempt > 0:
                        logger.info(f"{func.__name__} succeeded on attempt {attempt_num}")
                    
                    return result
                    
                except Exception as e:
                    last_exception = e
                    
                    logger.warning(
                        f"{func.__name__} failed on attempt {attempt_num}/{self.max_retries}: {str(e)}"
                    )
                    
                    # 如果不是最后一次尝试，等待后重试
                    if attempt_num < self.max_retries:
                        delay = self.retry_delay * attempt_num  # 指数退避
                        logger.info(f"Retrying {func.__name__} in {delay}s...")
                        await asyncio.sleep(delay)
                    else:
                        logger.error(f"All {self.max_retries} attempts failed for {func.__name__}")
            
            # 所有重试都失败了，抛出最后一个异常
            logger.error(f"Final exception for {func.__name__}: {str(last_exception)}")
            raise last_exception
        
        return wrapper


class ProxyErrorHandler:
    """代理错误处理器"""
    
    @staticmethod
    def is_retryable_error(exception: Exception) -> bool:
        """
        判断错误是否可重试
        
        Args:
            exception: 异常对象
            
        Returns:
            是否可重试
        """
        import httpx
        
        # 网络相关错误通常可重试
        if isinstance(exception, (httpx.TimeoutException, httpx.ConnectError, httpx.NetworkError)):
            return True
        
        # HTTP 状态码错误
        if isinstance(exception, httpx.HTTPStatusError):
            # 5xx 服务器错误通常可重试
            if 500 <= exception.response.status_code < 600:
                return True
            # 429 限流错误可重试
            if exception.response.status_code == 429:
                return True
            # 408 请求超时可重试
            if exception.response.status_code == 408:
                return True
        
        # 其他错误通常不重试
        return False
    
    @staticmethod
    def get_retry_delay(attempt: int, base_delay: float = 1.0) -> float:
        """
        计算重试延迟（指数退避）
        
        Args:
            attempt: 当前尝试次数（从 0 开始）
            base_delay: 基础延迟时间
            
        Returns:
            延迟时间（秒）
        """
        return base_delay * (2 ** attempt)
    
    @staticmethod
    def create_error_response(exception: Exception, attempt: int) -> Dict[str, Any]:
        """
        创建错误响应
        
        Args:
            exception: 异常对象
            attempt: 尝试次数
            
        Returns:
            错误响应字典
        """
        import httpx
        
        error_response = {
            "error": "Proxy request failed",
            "type": "proxy_error",
            "attempt": attempt,
            "detail": str(exception)
        }
        
        # 添加 HTTP 状态码信息
        if isinstance(exception, httpx.HTTPStatusError):
            error_response.update({
                "status_code": exception.response.status_code,
                "response_text": exception.response.text[:500]  # 限制长度
            })
        
        return error_response


# 便捷的装饰器实例
retry_on_failure = ProxyRetryHandler()
retry_with_backoff = ProxyRetryHandler(max_retries=5, retry_delay=2.0)


class SmartRetryHandler:
    """智能重试处理器，根据错误类型决定是否重试"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.error_handler = ProxyErrorHandler()
    
    def __call__(self, func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            last_exception = None
            
            for attempt in range(self.max_retries):
                attempt_num = attempt + 1
                
                try:
                    return await func(*args, **kwargs)
                    
                except Exception as e:
                    last_exception = e
                    
                    # 检查是否可重试
                    if not self.error_handler.is_retryable_error(e):
                        logger.warning(f"Non-retryable error in {func.__name__}: {str(e)}")
                        raise e
                    
                    logger.warning(
                        f"Retryable error in {func.__name__} (attempt {attempt_num}/{self.max_retries}): {str(e)}"
                    )
                    
                    # 如果不是最后一次尝试，等待后重试
                    if attempt_num < self.max_retries:
                        delay = self.error_handler.get_retry_delay(attempt, self.base_delay)
                        logger.info(f"Retrying {func.__name__} in {delay}s...")
                        await asyncio.sleep(delay)
                    else:
                        logger.error(f"All {self.max_retries} attempts failed for {func.__name__}")
            
            raise last_exception
        
        return wrapper


# 智能重试装饰器实例
smart_retry = SmartRetryHandler()
smart_retry_aggressive = SmartRetryHandler(max_retries=5, base_delay=0.5)


def with_retry_stats(func: Callable[..., T]) -> Callable[..., T]:
    """
    添加重试统计信息的装饰器
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    async def wrapper(*args, **kwargs) -> T:
        start_time = asyncio.get_event_loop().time()
        attempts = 0
        
        try:
            # 如果函数已经被重试装饰器包装，这里只记录统计信息
            result = await func(*args, **kwargs)
            
            end_time = asyncio.get_event_loop().time()
            duration = end_time - start_time
            
            logger.info(f"{func.__name__} completed in {duration:.3f}s")
            
            return result
            
        except Exception as e:
            end_time = asyncio.get_event_loop().time()
            duration = end_time - start_time
            
            logger.error(f"{func.__name__} failed after {duration:.3f}s: {str(e)}")
            raise
    
    return wrapper
