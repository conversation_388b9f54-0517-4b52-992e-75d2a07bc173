"""
请求处理器
整合各个服务，提供统一的请求处理接口
"""

import time
import json
import asyncio
from typing import Dict, Any, Optional, Tuple
from fastapi import Request, Response
from fastapi.responses import JSONResponse, StreamingResponse

from service.format_detector import CachedFormatDetector
from service.litellm_converter import Li<PERSON><PERSON><PERSON>onverter
from service.proxy_forwarder import ProxyForwarder
from models.conversion_models import LLMFormat, FormatDetectionResult, ConversionResult
from config.settings import settings
from log.logger import get_router_logger

logger = get_router_logger()


class RequestProcessor:
    """请求处理器类，整合所有核心服务"""
    
    def __init__(self):
        """初始化请求处理器"""
        self.format_detector = CachedFormatDetector()
        self.litellm_converter = LiteLLMConverter()
        self.proxy_forwarder = ProxyForwarder()
        
        # 性能监控
        self.request_count = 0
        self.total_process_time = 0.0
        self.error_count = 0
        
        logger.info("Request processor initialized with all core services")
    
    async def process_request(
        self, 
        request: Request, 
        path: str,
        request_id: Optional[str] = None
    ) -> Response:
        """
        处理单个请求的完整流程
        
        Args:
            request: FastAPI 请求对象
            path: 请求路径
            request_id: 请求ID（用于日志追踪）
            
        Returns:
            处理后的响应
        """
        start_time = time.time()
        if not request_id:
            request_id = f"{int(time.time() * 1000)}-{hash(str(request.url)) % 10000}"
        
        try:
            self.request_count += 1
            
            # 步骤1：格式检测
            detection_result = await self._detect_format(request, request_id)
            
            # 步骤2：请求数据提取
            request_data = await self._extract_request_data(request, request_id)
            
            # 步骤3：格式转换
            conversion_result = await self._convert_format(
                request_data, detection_result, request_id
            )
            
            # 步骤4：请求转发
            response = await self._forward_request(
                request, path, conversion_result, request_id
            )
            
            # 步骤5：响应后处理
            final_response = await self._post_process_response(
                response, detection_result, start_time, request_id
            )
            
            # 更新统计信息
            process_time = time.time() - start_time
            self.total_process_time += process_time
            
            return final_response
            
        except Exception as e:
            self.error_count += 1
            process_time = time.time() - start_time
            self.total_process_time += process_time
            
            logger.error(f"[{request_id}] Request processing failed: {str(e)}")
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Request processing failed",
                    "detail": str(e),
                    "request_id": request_id,
                    "process_time_ms": process_time * 1000
                }
            )
    
    async def _detect_format(
        self, 
        request: Request, 
        request_id: str
    ) -> FormatDetectionResult:
        """格式检测步骤"""
        try:
            detection_result = await self.format_detector.detect_format(request)
            
            logger.info(
                f"[{request_id}] Format detected: {detection_result.detected_format.value} "
                f"(confidence: {detection_result.confidence:.2f})"
            )
            
            return detection_result
            
        except Exception as e:
            logger.error(f"[{request_id}] Format detection failed: {str(e)}")
            # 返回未知格式作为降级处理
            return FormatDetectionResult(
                detected_format=LLMFormat.UNKNOWN,
                confidence=0.0,
                detection_method="error_fallback",
                metadata={"error": str(e)}
            )
    
    async def _extract_request_data(
        self, 
        request: Request, 
        request_id: str
    ) -> Optional[Dict[str, Any]]:
        """请求数据提取步骤"""
        try:
            if request.method not in ["POST", "PUT", "PATCH"]:
                return None
            
            body = await request.body()
            if not body:
                return None
            
            content_type = request.headers.get("content-type", "")
            
            if "application/json" in content_type:
                data = json.loads(body.decode())
                logger.debug(f"[{request_id}] Extracted JSON data with {len(data)} fields")
                return data
            elif "application/x-www-form-urlencoded" in content_type:
                form_data = await request.form()
                data = dict(form_data)
                logger.debug(f"[{request_id}] Extracted form data with {len(data)} fields")
                return data
            else:
                # 其他类型数据
                data = {"raw_data": body.decode("utf-8", errors="ignore")}
                logger.debug(f"[{request_id}] Extracted raw data ({len(body)} bytes)")
                return data
                
        except Exception as e:
            logger.warning(f"[{request_id}] Failed to extract request data: {str(e)}")
            return None
    
    async def _convert_format(
        self, 
        request_data: Optional[Dict[str, Any]], 
        detection_result: FormatDetectionResult,
        request_id: str
    ) -> ConversionResult:
        """格式转换步骤"""
        try:
            source_format = detection_result.detected_format
            
            if source_format == LLMFormat.UNKNOWN:
                logger.warning(f"[{request_id}] Unknown format, skipping conversion")
                return ConversionResult(
                    success=True,
                    converted_data=request_data,
                    original_format=source_format,
                    target_format=LLMFormat.OPENAI,
                    metadata={"conversion_skipped": True}
                )
            
            if not request_data:
                logger.warning(f"[{request_id}] No request data to convert")
                return ConversionResult(
                    success=True,
                    converted_data=None,
                    original_format=source_format,
                    target_format=LLMFormat.OPENAI
                )
            
            conversion_result = await self.litellm_converter.convert_to_openai_format(
                request_data, source_format
            )
            
            if conversion_result.success:
                logger.debug(f"[{request_id}] Format conversion successful")
            else:
                logger.error(f"[{request_id}] Format conversion failed: {conversion_result.error_message}")
            
            return conversion_result
            
        except Exception as e:
            logger.error(f"[{request_id}] Format conversion error: {str(e)}")
            return ConversionResult(
                success=False,
                error_message=str(e),
                original_format=detection_result.detected_format,
                target_format=LLMFormat.OPENAI
            )
    
    async def _forward_request(
        self, 
        request: Request, 
        path: str,
        conversion_result: ConversionResult,
        request_id: str
    ) -> Response:
        """请求转发步骤"""
        try:
            if not conversion_result.success:
                return JSONResponse(
                    status_code=400,
                    content={
                        "error": "Format conversion failed",
                        "detail": conversion_result.error_message,
                        "request_id": request_id
                    }
                )
            
            # 检查是否为流式请求
            is_stream = self._is_stream_request(request, conversion_result.converted_data)
            
            # 转发请求
            response = await self.proxy_forwarder.forward_request(
                method=request.method,
                path=f"/{path}",
                headers=dict(request.headers),
                data=conversion_result.converted_data,
                params=dict(request.query_params),
                stream=is_stream
            )
            
            logger.debug(f"[{request_id}] Request forwarded successfully (stream: {is_stream})")
            return response
            
        except Exception as e:
            logger.error(f"[{request_id}] Request forwarding failed: {str(e)}")
            return JSONResponse(
                status_code=502,
                content={
                    "error": "Request forwarding failed",
                    "detail": str(e),
                    "request_id": request_id
                }
            )
    
    async def _post_process_response(
        self, 
        response: Response, 
        detection_result: FormatDetectionResult,
        start_time: float,
        request_id: str
    ) -> Response:
        """响应后处理步骤"""
        try:
            process_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 添加处理信息到响应头
            if hasattr(response, 'headers'):
                response.headers["X-Request-ID"] = request_id
                response.headers["X-Process-Time"] = f"{process_time:.1f}ms"
                response.headers["X-Source-Format"] = detection_result.detected_format.value
                response.headers["X-Detection-Confidence"] = f"{detection_result.confidence:.2f}"
                response.headers["X-Proxy-Version"] = "1.0.0"
            
            logger.info(f"[{request_id}] Request completed in {process_time:.1f}ms")
            return response
            
        except Exception as e:
            logger.warning(f"[{request_id}] Response post-processing failed: {str(e)}")
            # 即使后处理失败，也返回原始响应
            return response
    
    def _is_stream_request(
        self, 
        request: Request, 
        data: Optional[Dict[str, Any]]
    ) -> bool:
        """判断是否为流式请求"""
        # 检查查询参数
        if request.query_params.get("stream") == "true":
            return True
        
        # 检查请求体中的 stream 参数
        if data and isinstance(data, dict) and data.get("stream") is True:
            return True
        
        # 检查 Accept 头部
        accept_header = request.headers.get("accept", "")
        if "text/event-stream" in accept_header:
            return True
        
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        avg_process_time = (
            self.total_process_time / self.request_count 
            if self.request_count > 0 else 0.0
        )
        
        error_rate = (
            self.error_count / self.request_count 
            if self.request_count > 0 else 0.0
        )
        
        return {
            "request_count": self.request_count,
            "error_count": self.error_count,
            "error_rate": error_rate,
            "avg_process_time_ms": avg_process_time * 1000,
            "total_process_time_s": self.total_process_time,
            "format_detector_stats": self.format_detector.get_detection_stats(),
            "format_detector_cache": self.format_detector.get_cache_stats(),
            "converter_formats": self.litellm_converter.get_supported_formats(),
            "forwarder_stats": self.proxy_forwarder.get_stats()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            forwarder_health = await self.proxy_forwarder.health_check()
            
            return {
                "status": "healthy",
                "components": {
                    "format_detector": "healthy",
                    "litellm_converter": "healthy", 
                    "proxy_forwarder": forwarder_health["status"]
                },
                "stats": self.get_stats()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "stats": self.get_stats()
            }


# 全局请求处理器实例
request_processor = RequestProcessor()
