# LLM 格式转换代理服务器

基于 LiteLLM 的智能 LLM 格式转换代理服务器，支持各种 LLM 格式的自动检测、转换和转发。

## 🚀 功能特性

- **智能格式检测** - 自动识别各种 LLM API 格式（Gemini、Claude、OpenAI、Anthropic等）
- **无缝格式转换** - 基于 LiteLLM 的可靠格式转换，支持100+LLM提供商
- **高性能代理** - 异步处理，支持流式和非流式响应
- **易于配置** - 基于环境变量的灵活配置管理
- **完整监控** - 健康检查、性能监控和详细日志记录

## 📋 系统要求

- Python 3.9+
- FastAPI
- LiteLLM
- httpx

## 🛠️ 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，配置代理服务器和目标服务器参数
```

### 3. 启动服务

```bash
python main.py
```

服务将在 `http://localhost:8002` 启动，并将请求转发到 `http://127.0.0.1:8001`。

## 📖 配置说明

### 基本配置

- `PROXY_HOST`: 代理服务监听地址（默认: 0.0.0.0）
- `PROXY_PORT`: 代理服务端口（默认: 8002）
- `TARGET_HOST`: 目标服务地址（默认: 127.0.0.1）
- `TARGET_PORT`: 目标服务端口（默认: 8001）

### 性能配置

- `MAX_CONCURRENT_REQUESTS`: 最大并发请求数（默认: 100）
- `REQUEST_TIMEOUT`: 请求超时时间（默认: 300秒）
- `CONNECTION_POOL_SIZE`: 连接池大小（默认: 20）

### 日志配置

- `LOG_LEVEL`: 日志级别（默认: INFO）
- `LOG_FORMAT`: 日志格式

## 🔧 API 端点

### 健康检查

```
GET /health
```

返回服务健康状态。

### 服务信息

```
GET /info
```

返回服务配置信息。

### 代理端点

```
ANY /{path:path}
```

代理所有请求到目标服务器，支持自动格式检测和转换。

## 🏗️ 架构设计

```
客户端请求 (任意LLM格式) 
    ↓
代理服务器 (端口 8002)
    ↓
格式检测器 (自动识别请求格式)
    ↓
LiteLLM 转换器 (转换为 OpenAI 格式)
    ↓
请求转发器 (发送到目标服务器)
    ↓
目标服务器 (端口 8001)
    ↓
响应返回给客户端
```

## 📝 开发说明

### 项目结构

```
llm_proxy_server/
├── main.py                    # 主入口文件
├── config/                    # 配置管理
├── core/                      # 核心应用逻辑
├── service/                   # 业务服务层
├── router/                    # API 路由
├── models/                    # 数据模型
├── handler/                   # 请求处理器
├── middleware/                # 中间件
├── log/                       # 日志配置
├── utils/                     # 工具函数
└── tests/                     # 测试用例
```

### 运行测试

```bash
pytest tests/
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
