# LLM 格式转换代理服务器配置示例
# 复制此文件为 .env 并根据需要修改配置

# ================================
# 代理服务器配置
# ================================
PROXY_HOST=0.0.0.0
PROXY_PORT=8002

# ================================
# 目标服务器配置
# ================================
TARGET_HOST=127.0.0.1
TARGET_PORT=8001
# TARGET_BASE_URL 会自动生成，也可以手动指定
# TARGET_BASE_URL=http://127.0.0.1:8001

# ================================
# LiteLLM 配置
# ================================
# LiteLLM API 密钥（如果需要）
LITELLM_API_KEY=

# LiteLLM 基础 URL（如果需要）
LITELLM_BASE_URL=

# ================================
# 性能配置
# ================================
# 最大并发请求数
MAX_CONCURRENT_REQUESTS=100

# 请求超时时间（秒）
REQUEST_TIMEOUT=300

# 连接池大小
CONNECTION_POOL_SIZE=20

# ================================
# 日志配置
# ================================
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# 日志格式
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ================================
# 代理配置（可选）
# ================================
# 是否使用 HTTP 代理
USE_PROXY=false

# HTTP 代理列表（用逗号分隔）
# PROXIES=http://proxy1:8080,http://proxy2:8080

# ================================
# 安全配置
# ================================
# 允许的跨域来源（用逗号分隔）
ALLOWED_ORIGINS=*

# API 密钥请求头名称
API_KEY_HEADER=Authorization

# ================================
# 格式检测配置
# ================================
# 格式检测置信度阈值
FORMAT_DETECTION_CONFIDENCE_THRESHOLD=0.7

# 是否启用格式检测缓存
ENABLE_FORMAT_CACHE=true

# ================================
# 监控配置
# ================================
# 是否启用性能监控
ENABLE_METRICS=true

# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=30
