"""
FastAPI 应用创建和配置模块
复用现有项目的应用创建模式
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from config.settings import settings
from log.logger import get_application_logger

logger = get_application_logger()


def setup_routers(app: FastAPI):
    """
    配置应用路由

    Args:
        app: FastAPI应用实例
    """
    try:
        from router.proxy_routes import router as proxy_router

        # 先添加特定的管理端点
        from router.proxy_routes import health_check, get_stats, debug_detect_format
        app.add_api_route("/health", health_check, methods=["GET"], tags=["management"])
        app.add_api_route("/stats", get_stats, methods=["GET"], tags=["management"])
        app.add_api_route("/debug/detect-format", debug_detect_format, methods=["POST"], tags=["debug"])

        # 最后添加通配符代理路由
        app.include_router(proxy_router, tags=["proxy"])

        logger.info("Routers configured successfully")

    except Exception as e:
        logger.error(f"Failed to configure routers: {str(e)}")
        raise


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    管理应用程序的启动和关闭事件
    
    Args:
        app: FastAPI应用实例
    """
    logger.info("LLM Proxy Server starting up...")
    
    try:
        # 启动时的初始化工作
        logger.info(f"Proxy server configuration:")
        logger.info(f"  - Listen on: {settings.PROXY_HOST}:{settings.PROXY_PORT}")
        logger.info(f"  - Target server: {settings.TARGET_BASE_URL}")
        logger.info(f"  - Log level: {settings.LOG_LEVEL}")
        logger.info(f"  - Max concurrent requests: {settings.MAX_CONCURRENT_REQUESTS}")
        logger.info(f"  - Request timeout: {settings.REQUEST_TIMEOUT}s")
        
        # 这里可以添加其他启动时的初始化工作
        # 例如：连接数据库、初始化缓存、预热模型等
        
    except Exception as e:
        logger.critical(f"Critical error during application startup: {str(e)}", exc_info=True)
        raise
    
    yield
    
    logger.info("LLM Proxy Server shutting down...")
    # 这里可以添加关闭时的清理工作


def create_app() -> FastAPI:
    """
    创建并配置 FastAPI 应用程序实例
    
    Returns:
        FastAPI: 配置好的 FastAPI 应用程序实例
    """
    
    # 创建 FastAPI 应用
    app = FastAPI(
        title="LLM Format Proxy Server",
        description="基于 LiteLLM 的智能 LLM 格式转换代理服务器",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json"
    )
    
    # 配置 CORS 中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=["*"],
        expose_headers=["*"],
        max_age=600,
    )
    
    # 添加健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        return JSONResponse(
            status_code=200,
            content={
                "status": "healthy",
                "service": "LLM Format Proxy Server",
                "version": "1.0.0",
                "target_server": settings.TARGET_BASE_URL
            }
        )
    
    # 添加配置信息端点
    @app.get("/info")
    async def server_info():
        """服务器信息端点"""
        return JSONResponse(
            status_code=200,
            content={
                "service": "LLM Format Proxy Server",
                "version": "1.0.0",
                "proxy_host": settings.PROXY_HOST,
                "proxy_port": settings.PROXY_PORT,
                "target_server": settings.TARGET_BASE_URL,
                "max_concurrent_requests": settings.MAX_CONCURRENT_REQUESTS,
                "request_timeout": settings.REQUEST_TIMEOUT,
                "log_level": settings.LOG_LEVEL
            }
        )
    
    # 配置路由
    setup_routers(app)

    # 这里将来会添加中间件配置
    # setup_middlewares(app)

    # 这里将来会添加异常处理器配置
    # setup_exception_handlers(app)
    
    logger.info("FastAPI application created successfully")
    
    return app
