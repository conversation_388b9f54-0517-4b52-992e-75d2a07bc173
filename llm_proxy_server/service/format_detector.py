"""
智能格式检测服务
基于现有项目的智能路由中间件逻辑，扩展支持更多 LLM 格式的检测
"""

import re
import json
import asyncio
from typing import Dict, List, Tuple, Optional, Any
from fastapi import Request
from urllib.parse import urlparse, parse_qs

from models.conversion_models import LLMFormat, FormatDetectionResult
from log.logger import get_format_detector_logger
from config.settings import settings

logger = get_format_detector_logger()


class FormatDetector:
    """智能格式检测器"""
    
    def __init__(self):
        """初始化格式检测器"""
        # URL 路径模式匹配规则
        self.path_patterns = {
            LLMFormat.GEMINI: [
                r"generateContent",
                r"streamGenerateContent", 
                r"v1beta/models",
                r"/gemini/",
                r"/vertex-express/"
            ],
            LLMFormat.OPENAI: [
                r"/v1/chat/completions",
                r"/v1/embeddings",
                r"/v1/images/generations",
                r"/v1/audio/speech",
                r"/v1/models",
                r"/openai/"
            ],
            LLMFormat.CLAUDE: [
                r"/v1/messages",
                r"/claude/",
                r"anthropic"
            ],
            LLMFormat.ANTHROPIC: [
                r"/v1/messages",
                r"/anthropic/",
                r"claude"
            ],
            LLMFormat.COHERE: [
                r"/v1/generate",
                r"/v1/chat",
                r"/cohere/",
                r"command"
            ],
            LLMFormat.HUGGINGFACE: [
                r"/hf/",
                r"/huggingface/",
                r"huggingface.co"
            ],
            LLMFormat.OLLAMA: [
                r"/ollama/",
                r"/api/generate",
                r"/api/chat"
            ]
        }
        
        # 请求头检测规则
        self.header_patterns = {
            LLMFormat.GEMINI: [
                ("x-goog-api-key", r".*"),
                ("authorization", r".*google.*"),
                ("user-agent", r".*gemini.*")
            ],
            LLMFormat.OPENAI: [
                ("authorization", r"Bearer sk-.*"),
                ("openai-organization", r".*"),
                ("user-agent", r".*openai.*")
            ],
            LLMFormat.CLAUDE: [
                ("x-api-key", r".*"),
                ("anthropic-version", r".*"),
                ("user-agent", r".*anthropic.*")
            ],
            LLMFormat.ANTHROPIC: [
                ("x-api-key", r".*"),
                ("anthropic-version", r".*"),
                ("user-agent", r".*claude.*")
            ],
            LLMFormat.COHERE: [
                ("authorization", r"Bearer .*"),
                ("user-agent", r".*cohere.*")
            ],
            LLMFormat.HUGGINGFACE: [
                ("authorization", r"Bearer hf_.*"),
                ("user-agent", r".*huggingface.*")
            ],
            LLMFormat.OLLAMA: [
                ("user-agent", r".*ollama.*"),
                ("content-type", r"application/json")
            ]
        }
        
        # 请求体字段检测规则
        self.body_field_patterns = {
            LLMFormat.GEMINI: [
                "contents",
                "generationConfig",
                "safetySettings"
            ],
            LLMFormat.OPENAI: [
                "messages",
                "temperature",
                "max_tokens",
                "top_p"
            ],
            LLMFormat.CLAUDE: [
                "messages",
                "max_tokens",
                "system"
            ],
            LLMFormat.ANTHROPIC: [
                "messages", 
                "max_tokens",
                "system"
            ],
            LLMFormat.COHERE: [
                "message",
                "chat_history",
                "conversation_id"
            ],
            LLMFormat.HUGGINGFACE: [
                "inputs",
                "parameters",
                "options"
            ],
            LLMFormat.OLLAMA: [
                "prompt",
                "model",
                "options"
            ]
        }
        
        logger.info("Format detector initialized with comprehensive detection rules")
    
    async def detect_format(self, request: Request) -> FormatDetectionResult:
        """
        检测请求的 LLM 格式
        
        Args:
            request: FastAPI 请求对象
            
        Returns:
            格式检测结果
        """
        try:
            logger.debug(f"Detecting format for {request.method} {request.url.path}")
            
            # 多维度检测
            path_scores = await self._analyze_path(request.url.path)
            header_scores = await self._analyze_headers(request.headers)
            body_scores = await self._analyze_body(request)
            
            # 计算综合得分
            final_format, confidence, metadata = self._calculate_final_format(
                path_scores, header_scores, body_scores
            )
            
            logger.debug(f"Format detection result: {final_format} (confidence: {confidence:.2f})")
            
            return FormatDetectionResult(
                detected_format=final_format,
                confidence=confidence,
                detection_method="multi_dimensional_analysis",
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"Format detection failed: {str(e)}")
            return FormatDetectionResult(
                detected_format=LLMFormat.UNKNOWN,
                confidence=0.0,
                detection_method="error_fallback",
                metadata={"error": str(e)}
            )
    
    async def _analyze_path(self, path: str) -> Dict[LLMFormat, float]:
        """分析 URL 路径"""
        scores = {fmt: 0.0 for fmt in LLMFormat}
        
        path_lower = path.lower()
        
        for format_type, patterns in self.path_patterns.items():
            for pattern in patterns:
                if re.search(pattern.lower(), path_lower):
                    # 不同模式的权重不同
                    if pattern.startswith("/v1/"):
                        scores[format_type] += 0.8  # 标准 API 路径高权重
                    elif "generate" in pattern.lower():
                        scores[format_type] += 0.9  # 生成相关路径高权重
                    elif pattern.startswith("/hf/") or pattern.startswith("/huggingface/"):
                        scores[format_type] += 0.9  # HuggingFace 特定路径高权重
                    elif pattern.startswith("/ollama/") or pattern.startswith("/api/"):
                        scores[format_type] += 0.8  # Ollama 特定路径高权重
                    else:
                        scores[format_type] += 0.6  # 其他模式中等权重
        
        # 特殊规则：基于现有项目的智能路由逻辑
        if self._is_gemini_format(path):
            scores[LLMFormat.GEMINI] += 0.9
        elif self._is_openai_format(path):
            scores[LLMFormat.OPENAI] += 0.9
        elif self._is_v1_format(path):
            scores[LLMFormat.OPENAI] += 0.7  # v1 格式通常是 OpenAI 兼容
        
        return scores
    
    async def _analyze_headers(self, headers) -> Dict[LLMFormat, float]:
        """分析请求头"""
        scores = {fmt: 0.0 for fmt in LLMFormat}
        
        for format_type, header_rules in self.header_patterns.items():
            for header_name, pattern in header_rules:
                header_value = headers.get(header_name, "").lower()
                if header_value and re.search(pattern.lower(), header_value):
                    scores[format_type] += 0.5
        
        return scores
    
    async def _analyze_body(self, request: Request) -> Dict[LLMFormat, float]:
        """分析请求体"""
        scores = {fmt: 0.0 for fmt in LLMFormat}
        
        try:
            # 获取请求体
            body = await self._get_request_body(request)
            if not body:
                return scores
            
            # 解析 JSON
            try:
                body_data = json.loads(body)
            except json.JSONDecodeError:
                return scores
            
            # 检查字段模式
            for format_type, field_patterns in self.body_field_patterns.items():
                field_matches = 0
                for field in field_patterns:
                    if field in body_data:
                        field_matches += 1
                
                # 计算字段匹配得分
                if field_matches > 0:
                    scores[format_type] = field_matches / len(field_patterns)
            
            # 特殊检测逻辑
            scores = self._apply_special_body_rules(body_data, scores)
            
        except Exception as e:
            logger.debug(f"Body analysis failed: {str(e)}")
        
        return scores
    
    def _calculate_final_format(
        self, 
        path_scores: Dict[LLMFormat, float],
        header_scores: Dict[LLMFormat, float], 
        body_scores: Dict[LLMFormat, float]
    ) -> Tuple[LLMFormat, float, Dict[str, Any]]:
        """计算最终格式和置信度"""
        
        # 权重配置
        path_weight = 0.4
        header_weight = 0.3
        body_weight = 0.3
        
        # 计算加权得分
        final_scores = {}
        for format_type in LLMFormat:
            if format_type == LLMFormat.UNKNOWN:
                continue
                
            final_scores[format_type] = (
                path_scores.get(format_type, 0.0) * path_weight +
                header_scores.get(format_type, 0.0) * header_weight +
                body_scores.get(format_type, 0.0) * body_weight
            )
        
        # 找到最高得分
        if not final_scores or max(final_scores.values()) == 0:
            return LLMFormat.UNKNOWN, 0.0, {"reason": "no_pattern_matched"}
        
        best_format = max(final_scores, key=final_scores.get)
        confidence = min(final_scores[best_format], 1.0)  # 确保不超过 1.0
        
        # 应用置信度阈值
        threshold = settings.FORMAT_DETECTION_CONFIDENCE_THRESHOLD
        if confidence < threshold:
            return LLMFormat.UNKNOWN, confidence, {
                "reason": "below_threshold",
                "threshold": threshold,
                "scores": final_scores
            }
        
        metadata = {
            "scores": final_scores,
            "path_scores": path_scores,
            "header_scores": header_scores,
            "body_scores": body_scores
        }
        
        return best_format, confidence, metadata

    def _is_gemini_format(self, path: str) -> bool:
        """检查是否为 Gemini 格式（基于现有项目逻辑）"""
        path_lower = path.lower()
        return (
            "generatecontent" in path_lower or
            "v1beta/models" in path_lower or
            "/gemini/" in path_lower or
            "/vertex-express/" in path_lower
        )

    def _is_openai_format(self, path: str) -> bool:
        """检查是否为 OpenAI 格式（基于现有项目逻辑）"""
        path_lower = path.lower()
        return "/openai/" in path_lower

    def _is_v1_format(self, path: str) -> bool:
        """检查是否为 v1 格式（基于现有项目逻辑）"""
        path_lower = path.lower()
        return "/v1/" in path_lower and "/openai/" not in path_lower

    def _apply_special_body_rules(
        self,
        body_data: Dict[str, Any],
        scores: Dict[LLMFormat, float]
    ) -> Dict[LLMFormat, float]:
        """应用特殊的请求体检测规则"""

        # Gemini 特殊检测
        if "contents" in body_data and isinstance(body_data["contents"], list):
            for content in body_data["contents"]:
                if isinstance(content, dict) and "parts" in content:
                    scores[LLMFormat.GEMINI] += 0.3
                    break

        # OpenAI 特殊检测
        if "messages" in body_data and isinstance(body_data["messages"], list):
            for message in body_data["messages"]:
                if isinstance(message, dict) and "role" in message and "content" in message:
                    scores[LLMFormat.OPENAI] += 0.2
                    break

        # Claude 特殊检测
        if "system" in body_data and "messages" in body_data:
            scores[LLMFormat.CLAUDE] += 0.3
            scores[LLMFormat.ANTHROPIC] += 0.3

        # Cohere 特殊检测
        if "chat_history" in body_data or "conversation_id" in body_data:
            scores[LLMFormat.COHERE] += 0.4

        # HuggingFace 特殊检测
        if "inputs" in body_data:
            scores[LLMFormat.HUGGINGFACE] += 0.5
            if "parameters" in body_data:
                scores[LLMFormat.HUGGINGFACE] += 0.3

        # Ollama 特殊检测
        if "prompt" in body_data and "model" in body_data:
            scores[LLMFormat.OLLAMA] += 0.6
        elif "messages" in body_data and isinstance(body_data.get("model"), str):
            # 检查是否为 Ollama 风格的消息格式
            model_name = body_data.get("model", "").lower()
            if any(ollama_model in model_name for ollama_model in ["llama", "mistral", "codellama", "vicuna"]):
                scores[LLMFormat.OLLAMA] += 0.4

        return scores

    async def _get_request_body(self, request: Request) -> Optional[str]:
        """获取请求体内容"""
        try:
            # 检查是否已经读取过请求体
            if hasattr(request, "_body"):
                return request._body.decode() if request._body else None

            # 读取请求体
            body = await request.body()
            return body.decode() if body else None

        except Exception as e:
            logger.debug(f"Failed to read request body: {str(e)}")
            return None

    def is_format_supported(self, format_name: str) -> bool:
        """检查格式是否支持"""
        try:
            format_enum = LLMFormat(format_name.lower())
            return format_enum != LLMFormat.UNKNOWN
        except ValueError:
            return False

    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return [fmt.value for fmt in LLMFormat if fmt != LLMFormat.UNKNOWN]

    def get_detection_stats(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        return {
            "supported_formats": self.get_supported_formats(),
            "path_patterns_count": sum(len(patterns) for patterns in self.path_patterns.values()),
            "header_patterns_count": sum(len(patterns) for patterns in self.header_patterns.values()),
            "body_field_patterns_count": sum(len(patterns) for patterns in self.body_field_patterns.values()),
            "confidence_threshold": settings.FORMAT_DETECTION_CONFIDENCE_THRESHOLD
        }


class CachedFormatDetector(FormatDetector):
    """带缓存的格式检测器"""

    def __init__(self, cache_size: int = 1000):
        """
        初始化带缓存的格式检测器

        Args:
            cache_size: 缓存大小
        """
        super().__init__()
        self.cache_size = cache_size
        self.cache = {}
        self.cache_hits = 0
        self.cache_misses = 0

        logger.info(f"Cached format detector initialized with cache size: {cache_size}")

    async def detect_format(self, request: Request) -> FormatDetectionResult:
        """带缓存的格式检测"""
        if not settings.ENABLE_FORMAT_CACHE:
            return await super().detect_format(request)

        # 生成缓存键
        cache_key = self._generate_cache_key(request)

        # 检查缓存
        if cache_key in self.cache:
            self.cache_hits += 1
            logger.debug(f"Cache hit for format detection: {cache_key}")
            return self.cache[cache_key]

        # 执行检测
        self.cache_misses += 1
        result = await super().detect_format(request)

        # 存储到缓存
        if len(self.cache) >= self.cache_size:
            # 简单的 LRU：删除第一个元素
            first_key = next(iter(self.cache))
            del self.cache[first_key]

        self.cache[cache_key] = result
        logger.debug(f"Cache miss, stored result for: {cache_key}")

        return result

    def _generate_cache_key(self, request: Request) -> str:
        """生成缓存键"""
        # 基于 URL 路径和方法生成简单的缓存键
        return f"{request.method}:{request.url.path}"

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = self.cache_hits / total_requests if total_requests > 0 else 0.0

        return {
            "cache_size": len(self.cache),
            "max_cache_size": self.cache_size,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate": hit_rate,
            "enabled": settings.ENABLE_FORMAT_CACHE
        }

    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        logger.info("Format detection cache cleared")
