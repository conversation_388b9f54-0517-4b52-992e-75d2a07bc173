#!/usr/bin/env python3
"""
端到端集成测试脚本
测试完整的格式检测 -> 转换 -> 转发工作流程
"""

import asyncio
import json
import sys
import time
from typing import Dict, Any
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

async def test_complete_workflow():
    """测试完整的工作流程"""
    print("Testing complete workflow...")
    try:
        from core.application import create_app
        
        app = create_app()
        
        # 测试用例：不同格式的请求
        test_cases = [
            {
                "name": "OpenAI Chat Completion",
                "path": "/v1/chat/completions",
                "method": "POST",
                "headers": {"authorization": "Bearer sk-test123"},
                "data": {
                    "model": "gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": "Hello"}],
                    "temperature": 0.7
                },
                "expected_format": "openai"
            },
            {
                "name": "Gemini Generate Content",
                "path": "/v1beta/models/gemini-pro:generateContent",
                "method": "POST",
                "headers": {"x-goog-api-key": "test-key"},
                "data": {
                    "contents": [
                        {
                            "role": "user",
                            "parts": [{"text": "Hello"}]
                        }
                    ],
                    "generationConfig": {"temperature": 0.7}
                },
                "expected_format": "gemini"
            },
            {
                "name": "Claude Messages",
                "path": "/v1/messages",
                "method": "POST",
                "headers": {"x-api-key": "test-key", "anthropic-version": "2023-06-01"},
                "data": {
                    "model": "claude-3-sonnet-20240229",
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 1000,
                    "system": "You are helpful"
                },
                "expected_format": "claude"
            }
        ]
        
        with TestClient(app) as client:
            passed_tests = 0
            
            for test_case in test_cases:
                print(f"\n  Testing: {test_case['name']}")
                
                try:
                    response = client.request(
                        method=test_case["method"],
                        url=test_case["path"],
                        headers=test_case["headers"],
                        json=test_case["data"]
                    )
                    
                    # 检查响应头中的格式信息
                    source_format = response.headers.get("X-Source-Format", "unknown")
                    confidence = response.headers.get("X-Detection-Confidence", "0.0")
                    process_time = response.headers.get("X-Process-Time", "0ms")
                    
                    print(f"    Format detected: {source_format} (confidence: {confidence})")
                    print(f"    Process time: {process_time}")
                    print(f"    Response status: {response.status_code}")
                    
                    # 验证格式检测是否正确
                    if source_format == test_case["expected_format"]:
                        print(f"    ✓ Format detection correct")
                        passed_tests += 1
                    elif source_format == "unknown" and float(confidence) < 0.5:
                        print(f"    ✓ Low confidence detection (acceptable)")
                        passed_tests += 1
                    else:
                        print(f"    ✗ Format detection incorrect: expected {test_case['expected_format']}, got {source_format}")
                    
                except Exception as e:
                    print(f"    ✗ Test failed: {str(e)}")
            
            success_rate = passed_tests / len(test_cases)
            print(f"\nWorkflow test results: {passed_tests}/{len(test_cases)} passed ({success_rate:.1%})")
            
            return success_rate >= 0.8  # 80% 成功率
        
    except Exception as e:
        print(f"✗ Complete workflow test failed: {e}")
        return False

async def test_performance_monitoring():
    """测试性能监控功能"""
    print("\nTesting performance monitoring...")
    try:
        from core.application import create_app
        
        app = create_app()
        
        with TestClient(app) as client:
            # 发送多个请求来测试性能监控
            start_time = time.time()
            
            for i in range(5):
                response = client.post(
                    "/v1/chat/completions",
                    json={
                        "model": "gpt-3.5-turbo",
                        "messages": [{"role": "user", "content": f"Test message {i}"}]
                    },
                    headers={"authorization": "Bearer sk-test123"}
                )
                
                # 检查性能头部
                if "X-Process-Time" in response.headers:
                    process_time = response.headers["X-Process-Time"]
                    print(f"  Request {i+1}: {process_time}")
                else:
                    print(f"  Request {i+1}: No process time header")
            
            total_time = time.time() - start_time
            print(f"  Total time for 5 requests: {total_time:.2f}s")
            
            # 检查统计信息
            stats_response = client.get("/stats")
            if stats_response.status_code == 200:
                stats = stats_response.json()
                print(f"  ✓ Stats endpoint accessible")
                
                # 检查统计信息结构
                if "format_detector" in stats and "proxy_forwarder" in stats:
                    print(f"  ✓ Performance stats available")
                    return True
                else:
                    print(f"  ✗ Performance stats incomplete")
                    return False
            else:
                print(f"  ✗ Stats endpoint failed: {stats_response.status_code}")
                return False
        
    except Exception as e:
        print(f"✗ Performance monitoring test failed: {e}")
        return False

async def test_error_handling_workflow():
    """测试错误处理工作流程"""
    print("\nTesting error handling workflow...")
    try:
        from core.application import create_app
        
        app = create_app()
        
        with TestClient(app) as client:
            # 测试无效的请求数据
            response = client.post(
                "/v1/chat/completions",
                json={"invalid": "data"},
                headers={"authorization": "Bearer sk-test123"}
            )
            
            print(f"  Invalid data response: {response.status_code}")
            
            # 检查错误响应格式
            if response.status_code >= 400:
                try:
                    error_data = response.json()
                    if "error" in error_data:
                        print(f"  ✓ Error response properly formatted")
                    else:
                        print(f"  ✗ Error response missing error field")
                        return False
                except json.JSONDecodeError:
                    print(f"  ✗ Error response not valid JSON")
                    return False
            
            # 测试空请求体
            response = client.post(
                "/v1/chat/completions",
                headers={"authorization": "Bearer sk-test123"}
            )
            
            print(f"  Empty body response: {response.status_code}")
            
            # 测试无效路径（应该被代理转发）
            response = client.get("/invalid/path/that/does/not/exist")
            
            print(f"  Invalid path response: {response.status_code}")
            
            # 由于我们的代理会尝试转发所有请求，这些测试主要验证错误处理机制
            print(f"  ✓ Error handling workflow functional")
            return True
        
    except Exception as e:
        print(f"✗ Error handling workflow test failed: {e}")
        return False

async def test_debug_endpoints():
    """测试调试端点"""
    print("\nTesting debug endpoints...")
    try:
        from core.application import create_app
        
        app = create_app()
        
        with TestClient(app) as client:
            # 测试格式检测调试端点
            response = client.post(
                "/debug/detect-format",
                json={
                    "model": "gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": "Debug test"}]
                },
                headers={"authorization": "Bearer sk-test123"}
            )
            
            if response.status_code == 200:
                debug_data = response.json()
                print(f"  Debug format detection: {debug_data['detected_format']} (confidence: {debug_data['confidence']:.2f})")
                
                required_fields = ["detected_format", "confidence", "detection_method"]
                for field in required_fields:
                    if field in debug_data:
                        print(f"    ✓ {field}: present")
                    else:
                        print(f"    ✗ {field}: missing")
                        return False
                
                print(f"  ✓ Debug endpoint working correctly")
                return True
            else:
                print(f"  ✗ Debug endpoint failed: {response.status_code}")
                return False
        
    except Exception as e:
        print(f"✗ Debug endpoints test failed: {e}")
        return False

async def test_concurrent_requests():
    """测试并发请求处理"""
    print("\nTesting concurrent requests...")
    try:
        from core.application import create_app

        app = create_app()

        def make_request(client, request_id):
            """发送单个请求（同步版本）"""
            response = client.post(
                "/v1/chat/completions",
                json={
                    "model": "gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": f"Concurrent test {request_id}"}]
                },
                headers={"authorization": "Bearer sk-test123"}
            )
            return response.status_code, response.headers.get("X-Request-ID", "unknown")

        with TestClient(app) as client:
            # 发送多个请求测试并发处理
            results = []
            for i in range(5):
                status_code, request_id = make_request(client, i)
                results.append((status_code, request_id))
                print(f"  Request {i+1}: Status {status_code}, ID {request_id}")

            # 检查所有请求是否都有唯一的请求ID
            request_ids = [result[1] for result in results if result[1] != "unknown"]
            unique_ids = set(request_ids)

            if len(unique_ids) == len(request_ids) and len(request_ids) > 0:
                print(f"  ✓ All requests have unique IDs ({len(unique_ids)} unique)")
                return True
            else:
                print(f"  ✗ Request ID uniqueness issue: {len(unique_ids)} unique out of {len(request_ids)}")
                return False

    except Exception as e:
        print(f"✗ Concurrent requests test failed: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 70)
    print("LLM Proxy Server - End-to-End Integration Test")
    print("=" * 70)
    
    tests = [
        ("Complete Workflow", test_complete_workflow),
        ("Performance Monitoring", test_performance_monitoring),
        ("Error Handling Workflow", test_error_handling_workflow),
        ("Debug Endpoints", test_debug_endpoints),
        ("Concurrent Requests", test_concurrent_requests),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All end-to-end tests passed! System is fully functional.")
        return 0
    elif passed >= total * 0.8:  # 80% 通过率也可以接受
        print("✅ Most end-to-end tests passed. System is likely working correctly.")
        return 0
    else:
        print("❌ Too many tests failed. Please check the system integration.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
