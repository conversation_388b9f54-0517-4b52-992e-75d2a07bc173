# 核心框架依赖
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# LiteLLM 核心库
litellm>=1.50.0

# HTTP 客户端和网络
httpx[socks]>=0.25.0
aiohttp>=3.9.0

# 环境变量和配置
python-dotenv>=1.0.0

# 日志和监控
structlog>=23.0.0

# 开发和测试依赖
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
httpx>=0.25.0  # 用于测试

# 类型检查
mypy>=1.6.0
types-requests

# 代码格式化和检查
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0

# 安全
cryptography>=41.0.0
