"""
转换相关的数据模型定义
定义各种 LLM 格式转换所需的数据结构
"""

from typing import Dict, List, Any, Optional, Union, Literal
from pydantic import BaseModel, Field
from enum import Enum


class LLMFormat(str, Enum):
    """支持的 LLM 格式枚举"""
    OPENAI = "openai"
    GEMINI = "gemini"
    CLAUDE = "claude"
    ANTHROPIC = "anthropic"
    COHERE = "cohere"
    HUGGINGFACE = "huggingface"
    OLLAMA = "ollama"
    UNKNOWN = "unknown"


class MessageRole(str, Enum):
    """消息角色枚举"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    TOOL = "tool"
    FUNCTION = "function"


class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: MessageRole
    content: Union[str, List[Dict[str, Any]]]
    name: Optional[str] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None


class ChatCompletionRequest(BaseModel):
    """聊天完成请求模型"""
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = Field(default=1.0, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(default=None, gt=0)
    top_p: Optional[float] = Field(default=1.0, ge=0.0, le=1.0)
    frequency_penalty: Optional[float] = Field(default=0.0, ge=-2.0, le=2.0)
    presence_penalty: Optional[float] = Field(default=0.0, ge=-2.0, le=2.0)
    stream: Optional[bool] = False
    stop: Optional[Union[str, List[str]]] = None
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[Union[str, Dict[str, Any]]] = None
    user: Optional[str] = None


class EmbeddingRequest(BaseModel):
    """嵌入请求模型"""
    model: str
    input: Union[str, List[str], List[int], List[List[int]]]
    encoding_format: Optional[Literal["float", "base64"]] = "float"
    dimensions: Optional[int] = None
    user: Optional[str] = None


class ImageGenerationRequest(BaseModel):
    """图像生成请求模型"""
    model: str
    prompt: str
    n: Optional[int] = Field(default=1, ge=1, le=10)
    size: Optional[str] = Field(default="1024x1024")
    quality: Optional[Literal["standard", "hd"]] = "standard"
    response_format: Optional[Literal["url", "b64_json"]] = "url"
    style: Optional[Literal["vivid", "natural"]] = None
    user: Optional[str] = None


class ConversionResult(BaseModel):
    """转换结果模型"""
    success: bool
    converted_data: Optional[Dict[str, Any]] = None
    original_format: Optional[LLMFormat] = None
    target_format: Optional[LLMFormat] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class FormatDetectionResult(BaseModel):
    """格式检测结果模型"""
    detected_format: LLMFormat
    confidence: float = Field(ge=0.0, le=1.0)
    detection_method: str
    metadata: Optional[Dict[str, Any]] = None


class LiteLLMConfig(BaseModel):
    """LiteLLM 配置模型"""
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    api_version: Optional[str] = None
    timeout: Optional[int] = Field(default=300, gt=0)
    max_retries: Optional[int] = Field(default=3, ge=0)
    custom_headers: Optional[Dict[str, str]] = None
    proxy: Optional[str] = None
    
    # 模型映射配置
    model_mapping: Optional[Dict[str, str]] = None
    
    # 提供商特定配置
    provider_configs: Optional[Dict[str, Dict[str, Any]]] = None


class StreamChunk(BaseModel):
    """流式响应块模型"""
    id: str
    object: str = "chat.completion.chunk"
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Optional[Dict[str, Any]] = None


class CompletionResponse(BaseModel):
    """完成响应模型"""
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, Any]
    system_fingerprint: Optional[str] = None


class EmbeddingResponse(BaseModel):
    """嵌入响应模型"""
    object: str = "list"
    data: List[Dict[str, Any]]
    model: str
    usage: Dict[str, Any]


class ImageGenerationResponse(BaseModel):
    """图像生成响应模型"""
    created: int
    data: List[Dict[str, Any]]


class ConversionError(Exception):
    """转换错误异常"""
    def __init__(self, message: str, source_format: Optional[str] = None, 
                 target_format: Optional[str] = None, original_error: Optional[Exception] = None):
        self.message = message
        self.source_format = source_format
        self.target_format = target_format
        self.original_error = original_error
        super().__init__(self.message)


class UnsupportedFormatError(ConversionError):
    """不支持的格式错误"""
    pass


class InvalidRequestError(ConversionError):
    """无效请求错误"""
    pass
