#!/usr/bin/env python3
"""
LLM 格式转换代理服务器主入口文件
基于 LiteLLM 的智能 LLM 格式转换代理服务器，支持各种 LLM 格式的自动检测、转换和转发
"""

import uvicorn
from dotenv import load_dotenv

# 在导入应用程序配置之前加载 .env 文件到环境变量
load_dotenv()

from core.application import create_app
from log.logger import get_main_logger

# 创建 FastAPI 应用实例
app = create_app()

def main():
    """主函数，用于启动代理服务器"""
    logger = get_main_logger()
    logger.info("Starting LLM Proxy Server...")
    
    # 从配置中获取服务器参数
    from config.settings import settings
    
    logger.info(f"Server will listen on {settings.PROXY_HOST}:{settings.PROXY_PORT}")
    logger.info(f"Target server: {settings.TARGET_HOST}:{settings.TARGET_PORT}")
    
    # 启动服务器
    uvicorn.run(
        app, 
        host=settings.PROXY_HOST, 
        port=settings.PROXY_PORT,
        log_level="info"
    )

if __name__ == "__main__":
    main()

# 当作为模块导入时也启动服务器（用于 python -m main）
if __name__ == "main":
    main()
