#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的 API 测试脚本
"""

import requests
import json

def test_simple():
    """简单测试"""
    url = "http://127.0.0.1:8001/v1/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer 1"
    }
    
    payload = {
        "model": "gemini-2.5-flash",
        "messages": [
            {
                "role": "user",
                "content": "Extract facts from: 我住在北京，是一名软件工程师"
            }
        ],
        "max_tokens": 100,
        "tools": []  # 禁用工具调用
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"完整响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if "choices" in data and len(data["choices"]) > 0:
                content = data["choices"][0]["message"]["content"]
                print(f"内容: '{content}'")
                print(f"内容长度: {len(content)}")
                print(f"内容类型: {type(content)}")
                
                if content == "":
                    print("❌ 内容为空字符串")
                elif content == "暂无返回":
                    print("❌ 返回默认文本")
                else:
                    print("✅ 内容正常")
            else:
                print("❌ 响应结构异常")
        else:
            print(f"❌ HTTP 错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_simple()
