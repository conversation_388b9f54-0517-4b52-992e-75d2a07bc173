#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查工具调用配置
"""

import requests
import json

def check_tools_config():
    """检查当前的工具调用配置"""
    url = "http://127.0.0.1:8001/api/config"
    headers = {
        "Authorization": "Bearer sk-123456"
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            config = response.json()
            
            print("🔧 当前工具调用相关配置:")
            print(f"TOOLS_CODE_EXECUTION_ENABLED: {config.get('TOOLS_CODE_EXECUTION_ENABLED', 'Not found')}")
            print(f"URL_CONTEXT_ENABLED: {config.get('URL_CONTEXT_ENABLED', 'Not found')}")
            print(f"URL_CONTEXT_MODELS: {config.get('URL_CONTEXT_MODELS', 'Not found')}")
            
            if config.get('TOOLS_CODE_EXECUTION_ENABLED', False):
                print("\n⚠️  工具调用已启用 - 这可能导致空响应问题")
                print("建议: 通过管理界面将 TOOLS_CODE_EXECUTION_ENABLED 设置为 false")
            else:
                print("\n✅ 工具调用已禁用")
                
        else:
            print(f"❌ 获取配置失败: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def disable_tools():
    """禁用工具调用"""
    url = "http://127.0.0.1:8001/api/config"
    headers = {
        "Authorization": "Bearer sk-123456",
        "Content-Type": "application/json"
    }
    
    data = {
        "TOOLS_CODE_EXECUTION_ENABLED": False,
        "URL_CONTEXT_ENABLED": False
    }
    
    try:
        response = requests.put(url, headers=headers, json=data, timeout=10)
        
        if response.status_code == 200:
            print("✅ 工具调用已成功禁用")
            return True
        else:
            print(f"❌ 禁用失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 工具调用配置检查器")
    print("=" * 50)
    
    print("\n1. 检查当前配置...")
    check_tools_config()
    
    print("\n" + "=" * 50)
    choice = input("是否要禁用工具调用? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', '是']:
        print("\n2. 禁用工具调用...")
        if disable_tools():
            print("\n3. 验证配置...")
            check_tools_config()
            print("\n🎉 配置更新完成！请重新测试 API 调用。")
        else:
            print("\n❌ 配置更新失败")
    else:
        print("\n取消操作")

if __name__ == "__main__":
    main()
